<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.38.0 (20140413.2041)
 -->
<!-- Title: Graphical Class Hierarchy Pages: 1 -->
<svg width="274pt" height="39pt"
 viewBox="0.00 0.00 274.00 39.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 35)">
<title>Graphical Class Hierarchy</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-35 270,-35 270,4 -4,4"/>
<!-- Node162 -->
<g id="node1" class="node"><title>Node162</title>
<polygon fill="white" stroke="#bfbfbf" points="0,-6 0,-25 60,-25 60,-6 0,-6"/>
<text text-anchor="middle" x="30" y="-13" font-family="Helvetica,sans-Serif" font-size="10.00">Exception</text>
</g>
<!-- Node0 -->
<g id="node2" class="node"><title>Node0</title>
<g id="a_node2"><a xlink:href="classpymoduleconnector_1_1extras_1_1regmap_1_1_regmap_error.xhtml" target="_top" xlink:title="Generic register map error. ">
<polygon fill="white" stroke="black" points="96,-0.5 96,-30.5 266,-30.5 266,-0.5 96,-0.5"/>
<text text-anchor="start" x="104" y="-18.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.regmap.</text>
<text text-anchor="middle" x="181" y="-7.5" font-family="Helvetica,sans-Serif" font-size="10.00">RegmapError</text>
</a>
</g>
</g>
<!-- Node162&#45;&gt;Node0 -->
<g id="edge1" class="edge"><title>Node162&#45;&gt;Node0</title>
<path fill="none" stroke="midnightblue" d="M70.1797,-15.5C78.206,-15.5 86.9439,-15.5 95.8694,-15.5"/>
<polygon fill="midnightblue" stroke="midnightblue" points="70.0824,-12.0001 60.0824,-15.5 70.0824,-19.0001 70.0824,-12.0001"/>
</g>
</g>
</svg>
