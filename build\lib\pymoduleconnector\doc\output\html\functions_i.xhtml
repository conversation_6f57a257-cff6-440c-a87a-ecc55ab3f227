<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_i"></a>- i -</h3><ul>
<li>inject_frame()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ad54c5bb15ab27d23428f57e011174985">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a11a03e24b3a5fcd01cfa62fb7439da78">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a11942b3a8832197300bd0fa041a9b941">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>is_csv_header()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_data_record.xhtml#ad1ab0c5b058c388cfe6d6d59d6332e8a">pymoduleconnector.moduleconnectorwrapper.DataRecord</a>
</li>
<li>is_open()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml#a1eb3c4cec3f2ff9d5a84966bbad60674">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a>
</li>
<li>is_recording()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_recorder.xhtml#a9686c460b0b212a734076e232ff62bee">pymoduleconnector.moduleconnectorwrapper.PyDataRecorder</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
