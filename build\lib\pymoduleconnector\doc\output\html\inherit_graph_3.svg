<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.38.0 (20140413.2041)
 -->
<!-- Title: Graphical Class Hierarchy Pages: 1 -->
<svg width="307pt" height="39pt"
 viewBox="0.00 0.00 307.00 39.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 35)">
<title>Graphical Class Hierarchy</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-35 303,-35 303,4 -4,4"/>
<!-- Node4 -->
<g id="node1" class="node"><title>Node4</title>
<polygon fill="white" stroke="#bfbfbf" points="0,-6 0,-25 81,-25 81,-6 0,-6"/>
<text text-anchor="middle" x="40.5" y="-13" font-family="Helvetica,sans-Serif" font-size="10.00">PyDataReader</text>
</g>
<!-- Node0 -->
<g id="node2" class="node"><title>Node0</title>
<g id="a_node2"><a xlink:href="classpymoduleconnector_1_1moduleconnector_1_1_data_reader.xhtml" target="_top" xlink:title="Inherits pymoduleconnector.moduleconnectorwrapper.PyDataReader. ">
<polygon fill="white" stroke="black" points="117,-0.5 117,-30.5 299,-30.5 299,-0.5 117,-0.5"/>
<text text-anchor="start" x="125" y="-18.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.moduleconnector.</text>
<text text-anchor="middle" x="208" y="-7.5" font-family="Helvetica,sans-Serif" font-size="10.00">DataReader</text>
</a>
</g>
</g>
<!-- Node4&#45;&gt;Node0 -->
<g id="edge1" class="edge"><title>Node4&#45;&gt;Node0</title>
<path fill="none" stroke="midnightblue" d="M91.1865,-15.5C99.3582,-15.5 108.045,-15.5 116.844,-15.5"/>
<polygon fill="midnightblue" stroke="midnightblue" points="91.1414,-12.0001 81.1414,-15.5 91.1413,-19.0001 91.1414,-12.0001"/>
</g>
</g>
</svg>
