<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">RecordingOptions</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.RecordingOptions Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>, including all inherited members.</p>
<table class="directory">
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>__init__</b>(self, args) (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a20fa9eabb0ed9ec8147d44738848854f">get_data_rate_limit</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a6217bf22e4808de72736154fc99c54fd">get_directory_split_size</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a36c3a4834bf15f78e57918e318086d4a">get_file_split_size</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a7df54e8220e70427c10478887935a096">get_flush_on_write</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a44620f5f8a32092b863d78e6baac9ad0">get_session_id</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a115bcb784f8909992cff589ec005722e">get_user_header</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a5f5788127ec56ee7d14ab848299988cb">set_data_rate_limit</a>(self, limit)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#acf02bee9c4142297511c556d635305d0">set_directory_split_size</a>(self, size)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#ad0c2444e6629f7334ae3859ad6946d4b">set_file_split_size</a>(self, size)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#aed473a5de7c674635120149aea07e3de">set_flush_on_write</a>(self, do_flush)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a3748f23da0a1e6582e7ac54ec11c400d">set_session_id</a>(self, id)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#ad1e010d865745ded5018f4dc3b70c6cf">set_user_header</a>(self, header)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>this</b> (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
