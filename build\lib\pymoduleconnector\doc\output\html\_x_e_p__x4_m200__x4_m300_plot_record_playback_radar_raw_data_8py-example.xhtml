<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: XEP_X4M200_X4M300_plot_record_playback_radar_raw_data.py</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">XEP_X4M200_X4M300_plot_record_playback_radar_raw_data.py</div>  </div>
</div><!--header-->
<div class="contents">
<p>Target module: X4M200,X4M300,X4M03Introduction: XeThru modules support both RF and baseband data output. This is an example of radar raw data manipulation. Developer can use Module Connecter API to read, record radar raw data, and also playback recorded data.</p>
<p>Command to run: "python XEP_X4M200_X4M300_plot_record_playback_radar_raw_data.py -d com8" or "python3 X4M300_printout_presence_state.py -d com8" change "com8" with your device name, using "--help" to see other options. Using TCP server address as device name is also supported, e.g. "python X4M200_sleep_record.py -d tcp://*************:3000".</p>
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">#!/usr/bin/env python</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="stringliteral">&quot;&quot;&quot; \example XEP_X4M200_X4M300_plot_record_playback_radar_raw_data.py</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="stringliteral">#Target module: X4M200,X4M300,X4M03</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="stringliteral">#Introduction: XeThru modules support both RF and baseband data output. This is an example of radar raw data manipulation. </span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="stringliteral">               Developer can use Module Connecter API to read, record radar raw data, and also playback recorded data. </span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="stringliteral">               </span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="stringliteral">#Command to run: &quot;python XEP_X4M200_X4M300_plot_record_playback_radar_raw_data.py -d com8&quot; or &quot;python3 X4M300_printout_presence_state.py -d com8&quot;</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="stringliteral">                 change &quot;com8&quot; with your device name, using &quot;--help&quot; to see other options.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="stringliteral">                 Using TCP server address as device name is also supported, e.g. </span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="stringliteral">                 &quot;python X4M200_sleep_record.py -d tcp://*************:3000&quot;.</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="stringliteral">&quot;&quot;&quot;</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="keyword">from</span> __future__ <span class="keyword">import</span> print_function, division</div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;</div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="keyword">import</span> sys</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="keyword">from</span> optparse <span class="keyword">import</span> OptionParser</div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="keyword">from</span> time <span class="keyword">import</span> sleep</div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="keyword">import</span> numpy <span class="keyword">as</span> np</div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="keyword">import</span> matplotlib.pyplot <span class="keyword">as</span> plt</div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="keyword">from</span> matplotlib.animation <span class="keyword">import</span> FuncAnimation</div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;</div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="keyword">import</span> pymoduleconnector</div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="keyword">from</span> pymoduleconnector <span class="keyword">import</span> DataType</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;__version__ = 3</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="keyword">def </span>reset(device_name):</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;    mc = <a class="code" href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml">pymoduleconnector.ModuleConnector</a>(device_name)</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;    xep = mc.get_xep()</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;    xep.module_reset()</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;    mc.close()</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;    sleep(3)</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">def </span>on_file_available(data_type, filename):</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;    print(<span class="stringliteral">&quot;new file available for data type: {}&quot;</span>.format(data_type))</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;    print(<span class="stringliteral">&quot;  |- file: {}&quot;</span>.format(filename))</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;    <span class="keywordflow">if</span> data_type == DataType.FloatDataType:</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        print(<span class="stringliteral">&quot;processing Float data from file&quot;</span>)</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">def </span>on_meta_file_available(session_id, meta_filename):</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;    print(<span class="stringliteral">&quot;new meta file available for recording with id: {}&quot;</span>.format(session_id))</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;    print(<span class="stringliteral">&quot;  |- file: {}&quot;</span>.format(meta_filename))</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="keyword">def </span>clear_buffer(mc):</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;    <span class="stringliteral">&quot;&quot;&quot;Clears the frame buffer&quot;&quot;&quot;</span></div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;    xep = mc.get_xep()</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;    <span class="keywordflow">while</span> xep.peek_message_data_float():</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;        xep.read_message_data_float()</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="keyword">def </span>simple_xep_plot(device_name, record=False, baseband=False):</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;    FPS = 10</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;    directory = <span class="stringliteral">&#39;.&#39;</span></div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;    reset(device_name)</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;    mc = <a class="code" href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml">pymoduleconnector.ModuleConnector</a>(device_name)</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    <span class="comment"># Assume an X4M300/X4M200 module and try to enter XEP mode</span></div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;    app = mc.get_x4m300()</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;    <span class="comment"># Stop running application and set module in manual mode.</span></div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;    <span class="keywordflow">try</span>:</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        app.set_sensor_mode(0x13, 0) <span class="comment"># Make sure no profile is running.</span></div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;    <span class="keywordflow">except</span> RuntimeError:</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;        <span class="comment"># Profile not running, OK</span></div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        <span class="keywordflow">pass</span></div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;    <span class="keywordflow">try</span>:</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;        app.set_sensor_mode(0x12, 0) <span class="comment"># Manual mode.</span></div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;    <span class="keywordflow">except</span> RuntimeError:</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;        <span class="comment"># Maybe running XEP firmware only?</span></div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;        <span class="keywordflow">pass</span></div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;    <span class="keywordflow">if</span> record:</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        recorder = mc.get_data_recorder()</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        recorder.subscribe_to_file_available(pymoduleconnector.AllDataTypes, on_file_available )</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;        recorder.subscribe_to_meta_file_available(on_meta_file_available)</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;    xep = mc.get_xep()</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;    <span class="comment"># Set DAC range</span></div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;    xep.x4driver_set_dac_min(900)</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;    xep.x4driver_set_dac_max(1150)</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    <span class="comment"># Set integration</span></div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    xep.x4driver_set_iterations(16)</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    xep.x4driver_set_pulses_per_step(26)</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    xep.x4driver_set_downconversion(int(baseband))</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    <span class="comment"># Start streaming of data</span></div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;    xep.x4driver_set_fps(FPS)</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    <span class="keyword">def </span>read_frame():</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        <span class="stringliteral">&quot;&quot;&quot;Gets frame data from module&quot;&quot;&quot;</span></div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;        d = xep.read_message_data_float()</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;        frame = np.array(d.data)</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;         <span class="comment"># Convert the resulting frame to a complex array if downconversion is enabled</span></div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;        <span class="keywordflow">if</span> baseband:</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;            n = len(frame)</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;            frame = frame[:n//2] + 1j*frame[n//2:]</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;        <span class="keywordflow">return</span> frame</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;    <span class="keyword">def </span>animate(i):</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        <span class="keywordflow">if</span> baseband:</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;            line.set_ydata(abs(read_frame())) <span class="comment"># update the data</span></div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        <span class="keywordflow">else</span>:</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;            line.set_ydata(read_frame())</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;        <span class="keywordflow">return</span> line,</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;    fig = plt.figure()</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;    fig.suptitle(<span class="stringliteral">&quot;example version %d &quot;</span>%(__version__))</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;    ax = fig.add_subplot(1,1,1)</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;    ax.set_ylim(0 <span class="keywordflow">if</span> baseband <span class="keywordflow">else</span> -0.03,0.03) <span class="comment">#keep graph in frame (FIT TO YOUR DATA)</span></div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;    frame = read_frame()</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;    <span class="keywordflow">if</span> baseband:</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;        frame = abs(frame)</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;    line, = ax.plot(frame)</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;    clear_buffer(mc)</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    <span class="keywordflow">if</span> record:</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;        recorder.start_recording(DataType.BasebandApDataType | DataType.FloatDataType, directory)</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;    ani = FuncAnimation(fig, animate, interval=FPS)</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    <span class="keywordflow">try</span>:</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;        plt.show()</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;    <span class="keywordflow">finally</span>:</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;        <span class="comment"># Stop streaming of data</span></div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;        xep.x4driver_set_fps(0)</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;<span class="keyword">def </span>playback_recording(meta_filename, baseband=False):</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;    print(<span class="stringliteral">&quot;Starting playback for {}&quot;</span> .format(meta_filename))</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;    player = <a class="code" href="classpymoduleconnector_1_1moduleconnector_1_1_data_player.xhtml">pymoduleconnector.DataPlayer</a>(meta_filename, -1)</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;    dur = player.get_duration()</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;    mc = <a class="code" href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml">pymoduleconnector.ModuleConnector</a>(player)</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;    xep = mc.get_xep()</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;    player.set_playback_rate(1.0)</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;    player.set_loop_mode_enabled(<span class="keyword">True</span>)</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;    player.play()</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    print(<span class="stringliteral">&quot;Duration(ms): {}&quot;</span>.format(dur))</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <span class="keyword">def </span>read_frame():</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        <span class="stringliteral">&quot;&quot;&quot;Gets frame data from module&quot;&quot;&quot;</span></div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;        d = xep.read_message_data_float()</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;        frame = np.array(d.data)</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;        <span class="keywordflow">if</span> baseband:</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;            n = len(frame)</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;            frame = frame[:n//2] + 1j*frame[n//2:]</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;        <span class="keywordflow">return</span> frame</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;    <span class="keyword">def </span>animate(i):</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;        <span class="keywordflow">if</span> baseband:</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;            line.set_ydata(abs(read_frame()))  <span class="comment"># update the data</span></div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        <span class="keywordflow">else</span>:</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;            line.set_ydata(read_frame())</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;        <span class="keywordflow">return</span> line,</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;    fig = plt.figure()</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;    fig.suptitle(<span class="stringliteral">&quot;Plot playback&quot;</span>)</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;    ax = fig.add_subplot(1,1,1)</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    frame = read_frame()</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;    line, = ax.plot(frame)</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;    ax.set_ylim(0 <span class="keywordflow">if</span> baseband <span class="keywordflow">else</span> -0.03,0.03) <span class="comment">#keep graph in frame (FIT TO YOUR DATA)</span></div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;    ani = FuncAnimation(fig, animate, interval=10)</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;    plt.show()</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;    player.stop()</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;<span class="keyword">def </span>main():</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;    parser = OptionParser()</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;    parser.add_option(</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;        <span class="stringliteral">&quot;-d&quot;</span>,</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;        <span class="stringliteral">&quot;--device&quot;</span>,</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;        dest=<span class="stringliteral">&quot;device_name&quot;</span>,</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;        help=<span class="stringliteral">&quot;device file to use&quot;</span>,</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;        metavar=<span class="stringliteral">&quot;FILE&quot;</span>)</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    parser.add_option(</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;        <span class="stringliteral">&quot;-b&quot;</span>,</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;        <span class="stringliteral">&quot;--baseband&quot;</span>,</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;        action=<span class="stringliteral">&quot;store_true&quot;</span>,</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;        default=<span class="keyword">False</span>,</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;        dest=<span class="stringliteral">&quot;baseband&quot;</span>,</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;        help=<span class="stringliteral">&quot;Enable baseband, rf data is default&quot;</span>)</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;    parser.add_option(</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;        <span class="stringliteral">&quot;-r&quot;</span>,</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;        <span class="stringliteral">&quot;--record&quot;</span>,</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;        action=<span class="stringliteral">&quot;store_true&quot;</span>,</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;        default=<span class="keyword">False</span>,</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;        dest=<span class="stringliteral">&quot;record&quot;</span>,</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;        help=<span class="stringliteral">&quot;Enable recording&quot;</span>)</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;    parser.add_option(</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;        <span class="stringliteral">&quot;-f&quot;</span>,</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;        <span class="stringliteral">&quot;--file&quot;</span>,</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;        dest=<span class="stringliteral">&quot;meta_filename&quot;</span>,</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;        metavar=<span class="stringliteral">&quot;FILE&quot;</span>,</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;        help=<span class="stringliteral">&quot;meta file from recording&quot;</span>)</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;    (options, args) = parser.parse_args()</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;    <span class="keywordflow">if</span> <span class="keywordflow">not</span> options.device_name:</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;        <span class="keywordflow">if</span>  options.meta_filename:</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;            playback_recording(options.meta_filename,</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;                    baseband=options.baseband)</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;        <span class="keywordflow">else</span>:</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;            parser.error(<span class="stringliteral">&quot;Missing -d or -f. See --help.&quot;</span>)</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;    <span class="keywordflow">else</span>:</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;        simple_xep_plot(options.device_name, record=options.record,</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;                baseband=options.baseband)</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;<span class="keywordflow">if</span> __name__ == <span class="stringliteral">&quot;__main__&quot;</span>:</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;   main()</div></div><!-- fragment --> </div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
