<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: X4M200_X4M300_manipulate_noisemap.py</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">X4M200_X4M300_manipulate_noisemap.py</div>  </div>
</div><!--header-->
<div class="contents">
<p>Target module: X4M200,X4M300Introduction: This is an example of how to use noisemap on X4M200 and X4M300 modules.</p>
<p>Command to run: "python X4M200_X4M300_manipulate_noisemap.py -d com8 -i x4m300" or "python3 X4M200_X4M300_manipulate_noisemap.py -d com8 i x4m300" Using TCP server address as device name is also supported, e.g. "python X4M200_sleep_record.py -d tcp://*************:3000".</p>
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">#!/usr/bin/env python</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"># -*- coding: utf-8 -*-</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="stringliteral">&quot;&quot;&quot; \example X4M200_X4M300_manipulate_noisemap.py</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="stringliteral">#Target module: X4M200,X4M300</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="stringliteral">#Introduction:  This is an example of how to use noisemap on X4M200 and X4M300 modules.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="stringliteral">               </span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="stringliteral">#Command to run:  &quot;python X4M200_X4M300_manipulate_noisemap.py -d com8 -i x4m300&quot; or &quot;python3 X4M200_X4M300_manipulate_noisemap.py -d com8 i x4m300&quot;</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="stringliteral">                 Using TCP server address as device name is also supported, e.g. </span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="stringliteral">                 &quot;python X4M200_sleep_record.py -d tcp://*************:3000&quot;.</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="stringliteral">&quot;&quot;&quot;</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="keyword">from</span> __future__ <span class="keyword">import</span> print_function</div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;</div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="keyword">import</span> time</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="keyword">from</span> optparse <span class="keyword">import</span> OptionParser</div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="keyword">import</span> sys</div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="keyword">import</span> pymoduleconnector</div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="keyword">from</span> <a class="code" href="namespacepymoduleconnector_1_1ids.xhtml">pymoduleconnector.ids</a> <span class="keyword">import</span> *</div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;</div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;</div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;parser = OptionParser()</div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;parser.add_option(</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;    <span class="stringliteral">&quot;-d&quot;</span>,</div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;    <span class="stringliteral">&quot;--device&quot;</span>,</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;    dest=<span class="stringliteral">&quot;device_name&quot;</span>,</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;    help=<span class="stringliteral">&quot;device file to use, example: python %s -d COM4&quot;</span> % sys.argv[0],</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;    metavar=<span class="stringliteral">&quot;FILE&quot;</span>)</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;parser.add_option(</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;    <span class="stringliteral">&quot;-i&quot;</span>,</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;    <span class="stringliteral">&quot;--interface&quot;</span>,</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;    dest=<span class="stringliteral">&quot;interface&quot;</span>,</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;    default=<span class="stringliteral">&quot;x4m300&quot;</span>,</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;    help=<span class="stringliteral">&quot;Interface to use. x4m300 or x4m200&quot;</span>,</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;    metavar=<span class="stringliteral">&quot;IF&quot;</span>)</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;(options, args) = parser.parse_args()</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;mc = <a class="code" href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml">pymoduleconnector.ModuleConnector</a>(options.device_name, 0)</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="comment"># Get the XEP interface</span></div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;xep = mc.get_xep()</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="keyword">def </span>ls():</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;    files = xep.find_all_files()</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;    <span class="keywordflow">return</span> set(zip(files.file_type_items, files.file_identifier_items))</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="comment"># Get the app interface and set profile</span></div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="keywordflow">if</span> options.interface == <span class="stringliteral">&#39;x4m300&#39;</span>:</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;    app = mc.get_x4m300()</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;    profile = XTS_ID_APP_PRESENCE_2</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="keywordflow">elif</span> options.interface == <span class="stringliteral">&#39;x4m200&#39;</span>:</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;    app = mc.get_x4m200()</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;    profile = XTS_ID_APP_RESPIRATION_2</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="keywordflow">else</span>:</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    <span class="keywordflow">raise</span> SystemExit(<span class="stringliteral">&quot;Interface not recognized.&quot;</span>)</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;<span class="comment"># Load a profile. Noisemap controls are stored associated with this profile,</span></div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="comment"># meaning setting/getting it without loading a profile doesn&#39;t work.</span></div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;app.load_profile(profile)</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;<span class="comment"># Set a detection zone. The noisemap requires a matching detection zone.</span></div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;app.set_detection_zone(1.0, 3.0)</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;<span class="comment"># Set noisemap controls</span></div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;ctrl = app.get_noisemap_control()</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;<span class="comment"># Enable use of noisemap</span></div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;ctrl |=  XTID_NOISEMAP_CONTROL_ENABLE</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;<span class="comment"># Enable adaptive noisemap</span></div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;ctrl |=  XTID_NOISEMAP_CONTROL_ADAPTIVE</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;<span class="comment"># Don&#39;t reinitialize noisemap if stored, and store it after</span></div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;<span class="comment"># initialization if not present.</span></div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;ctrl &amp;= ~XTID_NOISEMAP_CONTROL_INIT_ON_RESET</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;app.set_noisemap_control(ctrl)</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;files = ls()</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;<span class="keywordflow">if</span> (XTFILE_TYPE_NOISEMAP_FAST, 0) <span class="keywordflow">in</span> files:</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;    xep.delete_file(XTFILE_TYPE_NOISEMAP_FAST, 0)</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;<span class="keywordflow">if</span> (XTFILE_TYPE_NOISEMAP_SLOW, 0) <span class="keywordflow">in</span> files:</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    xep.delete_file(XTFILE_TYPE_NOISEMAP_SLOW, 0)</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;print(<span class="stringliteral">&#39;Initializing a noisemap. Vacate the premises.Please wait for ~2min.&#39;</span>)</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;time.sleep(5)</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;status = app.set_sensor_mode(XTID_SM_RUN, 0)</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;time.sleep(130)</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;status = app.set_sensor_mode(XTID_SM_STOP, 0)</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;print(<span class="stringliteral">&#39;Done.&#39;</span>)</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;time.sleep(1)</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;files = ls()</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;<span class="keywordflow">if</span> (XTFILE_TYPE_NOISEMAP_FAST, 0) <span class="keywordflow">not</span> <span class="keywordflow">in</span> files:</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;    <span class="keywordflow">raise</span> SystemExit(<span class="stringliteral">&quot;Did not store a fast noisemap&quot;</span>)</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;<span class="keywordflow">if</span> (XTFILE_TYPE_NOISEMAP_SLOW, 0) <span class="keywordflow">not</span> <span class="keywordflow">in</span> files:</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;    <span class="keywordflow">raise</span> SystemExit(<span class="stringliteral">&quot;Did not store a slow noisemap&quot;</span>)</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;nm_fast = xep.get_file(XTFILE_TYPE_NOISEMAP_FAST, 0)</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;nm_slow = xep.get_file(XTFILE_TYPE_NOISEMAP_SLOW, 0)</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;status = app.load_profile(profile)</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;status = app.set_detection_zone(1, 2)</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;xep.delete_file(XTFILE_TYPE_NOISEMAP_FAST, 0)</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;xep.delete_file(XTFILE_TYPE_NOISEMAP_SLOW, 0)</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;print(<span class="stringliteral">&#39;Initializing another one.Please wait for 130s&#39;</span>)</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;app.set_sensor_mode(XTID_SM_RUN, 0)</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;time.sleep(130)</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;app.set_sensor_mode(XTID_SM_STOP, 0)</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;print(<span class="stringliteral">&#39;Done.&#39;</span>)</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;time.sleep(1)</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;files = ls()</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="keywordflow">if</span> (XTFILE_TYPE_NOISEMAP_FAST, 0) <span class="keywordflow">not</span> <span class="keywordflow">in</span> files:</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;    <span class="keywordflow">raise</span> SystemExit(<span class="stringliteral">&quot;Did not store a fast noisemap&quot;</span>)</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;<span class="keywordflow">if</span> (XTFILE_TYPE_NOISEMAP_SLOW, 0) <span class="keywordflow">not</span> <span class="keywordflow">in</span> files:</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;    <span class="keywordflow">raise</span> SystemExit(<span class="stringliteral">&quot;Did not store a slow noisemap&quot;</span>)</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;short_nm_fast = xep.get_file(XTFILE_TYPE_NOISEMAP_FAST, 0)</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;short_nm_slow = xep.get_file(XTFILE_TYPE_NOISEMAP_SLOW, 0)</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;xep.delete_file(XTFILE_TYPE_NOISEMAP_FAST, 0)</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;xep.delete_file(XTFILE_TYPE_NOISEMAP_SLOW, 0)</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;<span class="comment"># Once more, with a restored previous noisemap.</span></div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;<span class="comment"># Upload noisemap to module flash.</span></div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;xep.set_file(XTFILE_TYPE_NOISEMAP_FAST, 0, nm_fast)</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;xep.set_file(XTFILE_TYPE_NOISEMAP_SLOW, 0, nm_slow)</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;time.sleep(1)</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;print(<span class="stringliteral">&#39;Starting profile with the first noisemap.&#39;</span>)</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;app.load_profile(profile)</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;<span class="comment"># This must match the detection zone the noisemap was made with for the</span></div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;<span class="comment"># restoring to work.</span></div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;app.set_detection_zone(1.0, 3.0)</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;print(<span class="stringliteral">&#39;Initialization should now only be around 20 seconds.&#39;</span>)</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;app.set_sensor_mode(XTID_SM_RUN, 0)</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;time.sleep(20)</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;print(<span class="stringliteral">&#39;Initialization done.&#39;</span>)</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;print(<span class="stringliteral">&#39;Getting a list of noisemaps as they evolve over time.&#39;</span>)</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;nms_fast = []</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;nms_slow = []</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;<span class="keywordflow">for</span> ix <span class="keywordflow">in</span> range(5):</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;    time.sleep(50)</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    app.store_noisemap()</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    <span class="comment"># Store noisemap takes around a second to complete, pluss a varying</span></div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;    <span class="comment"># amount of time for the flash writing to catch up.</span></div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;    time.sleep(10)</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;    nms_fast.append(xep.get_file(XTFILE_TYPE_NOISEMAP_FAST, 0))</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;    nms_slow.append(xep.get_file(XTFILE_TYPE_NOISEMAP_SLOW, 0))</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;app.set_sensor_mode(XTID_SM_STOP, 0)</div></div><!-- fragment --> </div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
