from optparse import <PERSON>tion<PERSON><PERSON><PERSON>
from time import sleep
from collections import deque
import sys

import numpy as np
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QHBoxLayout
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QCloseEvent
import pyqtgraph as pg

import pymoduleconnector
from pymoduleconnector import DataType

__version__ = 3

def reset(device_name):
    mc = pymoduleconnector.ModuleConnector(device_name)
    xep = mc.get_xep()
    xep.module_reset()
    mc.close()
    sleep(3)

def clear_buffer(mc):
    """Clears the frame buffer"""
    xep = mc.get_xep()
    while xep.peek_message_data_float():
        xep.read_message_data_float()

class RadarVisualizationWindow(QMainWindow):
    def __init__(self, device_name, baseband=False):
        super().__init__()
        self.device_name = device_name
        self.baseband = baseband
        self.FPS = 30
        self.buffer_size = 100  # Number of frames to keep in the flowing display

        # Initialize hardware
        self.mc = None
        self.xep = None
        self.init_hardware()

        # Initialize UI
        self.init_ui()

        # Initialize data structures
        self.frame_buffer = deque(maxlen=self.buffer_size)
        self.current_frame = None

        # Initialize signal processing variables
        self.Wone = None  # For recursive canceller
        self.Yone = None  # For recursive canceller
        self.dc_history = deque(maxlen=10)  # For DC component estimation

        # Setup timer for real-time updates
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_plots)
        self.timer.start(500 // self.FPS)  # Convert FPS to milliseconds

    def init_hardware(self):
        """Initialize the XEP hardware connection"""
        reset(self.device_name)
        self.mc = pymoduleconnector.ModuleConnector(self.device_name)

        # Assume an X4M300/X4M200 module and try to enter XEP mode
        app = self.mc.get_x4m300()
        # Stop running application and set module in manual mode.
        try:
            app.set_sensor_mode(0x13, 0) # Make sure no profile is running.
        except RuntimeError:
            # Profile not running, OK
            pass

        try:
            app.set_sensor_mode(0x12, 0) # Manual mode.
        except RuntimeError:
            # Maybe running XEP firmware only?
            pass

        self.xep = self.mc.get_xep()
        # Set DAC range
        self.xep.x4driver_set_dac_min(0)
        self.xep.x4driver_set_dac_max(1100)

        # Set integration
        self.xep.x4driver_set_iterations(16)
        self.xep.x4driver_set_pulses_per_step(26)

        self.xep.x4driver_set_downconversion(int(self.baseband))
        # Start streaming of data
        self.xep.x4driver_set_fps(self.FPS)

        # Clear buffer
        clear_buffer(self.mc)

    def init_ui(self):
        """Initialize the user interface"""
        pg.setConfigOption('background', 'w')
        self.setWindowTitle(f"XEP Radar Visualization v{__version__}")
        self.setGeometry(100, 100, 1200, 600)

        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)

        # Left panel - 2D flowing colormap
        self.left_widget = pg.PlotWidget()
        # 顯示所有四個軸線
        self.left_widget.showAxis('top')
        self.left_widget.showAxis('right')

        # 設置標籤（或不設置，根據您之前的要求）
        self.left_widget.setLabel('left', 'Time (Frame Number)', units='frames')
        self.left_widget.setLabel('bottom', None) # 底部不顯示標籤文字
        self.left_widget.setLabel('top', 'Depth (Sample Index)', units='samples')
        # 其他設置
        self.left_widget.setTitle('2D Flowing Colormap (Right-to-Left)')
        self.left_widget.invertY(True)
        self.left_widget.showGrid(x=True, y=True, alpha=0.3)

        # =========================================================
        # == 新增功能：隱藏所有軸的刻度數字，但保留刻度線 ==
        # =========================================================
        axis_names = ('bottom', 'right')
        for name in axis_names:
            axis = self.left_widget.getAxis(name)
            axis.tickStrings = lambda values, scale, spacing: ['' for v in values]
        # =========================================================


        # Create ImageItem for the 2D colormap
        self.image_item = pg.ImageItem()
        self.left_widget.addItem(self.image_item)

        # Add colorbar (if available in this PyQtGraph version)
        try:
            self.colorbar = pg.ColorBarItem(
                values=(0, 1),
                colorMap=pg.colormap.get('viridis'),
                width=20,
                interactive=False
            )
            self.colorbar.setImageItem(self.image_item, insert_in=self.left_widget)
        except AttributeError:
            # Fallback for older PyQtGraph versions without ColorBarItem
            self.colorbar = None
            print("ColorBarItem not available in this PyQtGraph version")

        # Right panel - Real-time vertical plot
        self.right_widget = pg.PlotWidget()
        self.right_widget.showAxis('top')
        self.right_widget.showAxis('right')
        self.right_widget.setLabel('left', 'Amplitude', units='V')
        self.right_widget.setLabel('bottom', None)
        self.right_widget.setTitle('Real-time Frame')
        self.right_widget.invertY(True)
        # =========================================================
        # == 新增功能：隱藏所有軸的刻度數字，但保留刻度線 ==
        # =========================================================
        axis_names = ('bottom', 'right')
        for name in axis_names:
            axis = self.right_widget.getAxis(name)
            axis.tickStrings = lambda values, scale, spacing: ['' for v in values]
        # =========================================================

        # Enable grid for better readability
        self.right_widget.showGrid(x=True, y=True, alpha=0.3)

        # Create plot curve for real-time data
        self.real_time_curve = self.right_widget.plot(pen='r', width=5)


        # Add widgets to layout
        main_layout.addWidget(self.left_widget, 5)
        main_layout.addWidget(self.right_widget, 1)

    def read_frame(self):
        """Gets frame data from module"""
        try:
            d = self.xep.read_message_data_float()
            frame = np.array(d.data)
            # Convert the resulting frame to a complex array if downconversion is enabled
            if self.baseband:
                n = len(frame)
                frame = frame[:n//2] + 1j*frame[n//2:]
                frame = abs(frame)  # Take magnitude for baseband
            return frame
        except Exception as e:
            print(f"Error reading frame: {e}")
            return None

    def remove_dc_component(self, frame):
        """
        Layer 1: DC Component Removal
        Removes the DC (direct current) component from the radar signal
        """
        if frame is None:
            return None

        # Add current frame to history for DC estimation
        self.dc_history.append(np.mean(frame))

        # Calculate DC component as moving average
        if len(self.dc_history) > 0:
            dc_component = np.mean(list(self.dc_history))
        else:
            dc_component = 0

        # Remove DC component
        processed_frame = frame - dc_component
        return processed_frame

    def once_recursive_canceller(self, chirps, k=0.95):
        """
        Layer 2: Static Clutter Removal
        Recursive canceller function to remove static clutter from ground-penetrating radar data

        Args:
            chirps: Input radar frame data
            k: Cancellation factor (0 < k < 1), higher values = more aggressive cancellation
        """
        if chirps is None:
            return None

        # Initialize on first call
        if self.Wone is None:
            self.Wone = np.zeros_like(chirps)
        if self.Yone is None:
            self.Yone = np.zeros_like(chirps)

        # Ensure arrays have the same shape
        if self.Wone.shape != chirps.shape:
            self.Wone = np.zeros_like(chirps)

        # Apply recursive canceller algorithm
        self.Yone = chirps - (1 - k) * self.Wone
        self.Wone = self.Yone + self.Wone

        return self.Yone.copy()

    def apply_gain_compensation(self, frame, depth_segments=3):
        """
        Layer 3: Gain Compensation
        Applies different gain compensation for different depth segments
        to enhance signal visibility in ground-penetrating radar

        Args:
            frame: Input radar frame data
            depth_segments: Number of depth segments for different gain levels
        """
        if frame is None:
            return None

        compensated_frame = frame.copy()
        frame_length = len(frame)
        segment_size = frame_length // depth_segments

        # Apply increasing gain with depth to compensate for signal attenuation
        for i in range(depth_segments):
            start_idx = i * segment_size
            end_idx = (i + 1) * segment_size if i < depth_segments - 1 else frame_length

            # Exponential gain increase with depth
            gain_factor = 1.0 + (i * 0.5)  # Increase gain by 50% per segment
            compensated_frame[start_idx:end_idx] *= gain_factor

        return compensated_frame

    def process_signal_pipeline(self, raw_frame):
        """
        Complete three-layer signal processing pipeline

        Args:
            raw_frame: Raw radar frame data

        Returns:
            Processed frame data
        """
        if raw_frame is None:
            return None

        # Layer 1: DC Component Removal
        frame_dc_removed = self.remove_dc_component(raw_frame)

        # Layer 2: Static Clutter Removal
        frame_clutter_removed = self.once_recursive_canceller(raw_frame)

        # Layer 3: Gain Compensation
        frame_gain_compensated = self.apply_gain_compensation(frame_clutter_removed, depth_segments=5)

        return frame_gain_compensated

    def update_plots(self):
        """Update both plots with new data"""
        try:
            # Read new frame
            new_frame = self.read_frame()
            if new_frame is None:
                return

            # Apply three-layer signal processing pipeline
            processed_frame = self.process_signal_pipeline(new_frame)
            if processed_frame is None:
                processed_frame = new_frame  # Fallback to raw frame

            self.current_frame = processed_frame

            # Add to buffer for flowing display (use processed frame)
            self.frame_buffer.append(processed_frame)

            # Update 2D flowing colormap (left panel)
            if len(self.frame_buffer) > 1:
                # Stack frames to create 2D array
                stacked_data = np.array(list(self.frame_buffer))

                # Update image
                self.image_item.setImage(stacked_data, autoLevels=True)

                # Update colorbar range (if available)
                if self.colorbar is not None:
                    vmin, vmax = stacked_data.min(), stacked_data.max()
                    self.colorbar.setLevels((vmin, vmax))

            # Update real-time plot (right panel) - rotated 90 degrees
            if self.current_frame is not None:
                # For 90-degree rotation, we plot amplitude vs sample index
                y_data = np.arange(len(self.current_frame))
                x_data = self.current_frame

                self.real_time_curve.setData(x_data, y_data)

                # Auto-scale the plot
                y_range = [y_data.min() * 1.1, y_data.max() * 1.1] if len(y_data) > 0 else [-0.03, 0.03]
                if not self.baseband and y_range[1] - y_range[0] < 0.06:
                    y_range = [-0.03, 0.03]  # Default range for non-baseband
                self.right_widget.setYRange(y_range[0], y_range[1])

        except Exception as e:
            print(f"Error updating plots: {e}")

    def closeEvent(self, event: QCloseEvent):
        """Handle window close event"""
        try:
            if self.timer:
                self.timer.stop()
            if self.xep:
                # Stop streaming of data
                self.xep.x4driver_set_fps(0)
            if self.mc:
                self.mc.close()
        except Exception as e:
            print(f"Error during cleanup: {e}")
        event.accept()


def simple_xep_plot(device_name, baseband=False):
    """Create and run the PyQtGraph-based radar visualization"""
    # Create QApplication if it doesn't exist
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    # Create and show the main window
    window = RadarVisualizationWindow(device_name, baseband)
    window.show()

    # Run the application
    try:
        app.exec()
    except KeyboardInterrupt:
        print("Application interrupted by user")
    finally:
        window.close()


def main():
    parser = OptionParser()
    parser.add_option(
        "-d",
        "--device",
        dest="device_name",
        help="device file to use",
        metavar="FILE")
    parser.add_option(
        "-b",
        "--baseband",
        action="store_true",
        default=True,
        dest="baseband",
        help="Enable baseband, rf data is default")



    (options, _) = parser.parse_args()

    simple_xep_plot(options.device_name,  baseband=options.baseband)

if __name__ == "__main__":
   main()
