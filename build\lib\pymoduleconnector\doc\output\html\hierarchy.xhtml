<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: Class Hierarchy</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Class Hierarchy</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">
<p><a href="inherits.xhtml">Go to the graphical class hierarchy</a></p>
This inheritance list is sorted roughly, but not completely, alphabetically:</div><div class="directory">
<div class="levels">[detail level <span onclick="javascript:toggleLevel(1);">1</span><span onclick="javascript:toggleLevel(2);">2</span><span onclick="javascript:toggleLevel(3);">3</span>]</div><table class="directory">
<tr id="row_0_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_0_" class="arrow" onclick="toggleFolder('0_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>pymoduleconnector.moduleconnectorwrapper._object</b></td><td class="desc"></td></tr>
<tr id="row_0_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_ap_data.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.BasebandApData</a></td><td class="desc"></td></tr>
<tr id="row_0_1_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_iq_data.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.BasebandIqData</a></td><td class="desc"></td></tr>
<tr id="row_0_2_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_data_record.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.DataRecord</a></td><td class="desc">Encapsulates data and information about one data record on disk </td></tr>
<tr id="row_0_3_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_data_type.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.DataType</a></td><td class="desc">Proxy of C++ XeThru::DataType class </td></tr>
<tr id="row_0_4_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.DetectionZone</a></td><td class="desc">Representation of the detection zone </td></tr>
<tr id="row_0_5_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone_limits.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.DetectionZoneLimits</a></td><td class="desc">Is an aggrgation of parameters used to represent the detection zone limits </td></tr>
<tr id="row_0_6_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_files.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.Files</a></td><td class="desc"></td></tr>
<tr id="row_0_7_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_frame_area.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.FrameArea</a></td><td class="desc"></td></tr>
<tr id="row_0_8_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_io_pin_control.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.IoPinControl</a></td><td class="desc">Representation of io pin control configuration </td></tr>
<tr id="row_0_9_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_periodic_noisemap_store.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.PeriodicNoisemapStore</a></td><td class="desc">Representation of periodic noisemap store parameters </td></tr>
<tr id="row_0_10_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.PreferredSplitSize</a></td><td class="desc">The <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml" title="The PreferredSplitSize class allows specifying a split size. ">PreferredSplitSize</a> class allows specifying a split size </td></tr>
<tr id="row_0_11_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_byte_data.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.PulseDopplerByteData</a></td><td class="desc">Represents one half or one range bin of pulse-Doppler in byte format </td></tr>
<tr id="row_0_12_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_float_data.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.PulseDopplerFloatData</a></td><td class="desc">Represents one half or one range bin of pulse-Doppler in float format </td></tr>
<tr id="row_0_13_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_player.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.PyDataPlayer</a></td><td class="desc"></td></tr>
<tr id="row_0_14_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a></td><td class="desc">The <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml" title="The PyDataReader class allows reading of xethru data records from a recording. ">PyDataReader</a> class allows reading of xethru data records from a recording </td></tr>
<tr id="row_0_15_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_recorder.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.PyDataRecorder</a></td><td class="desc">The DataRecorder class allows recording of xethru data types </td></tr>
<tr id="row_0_16_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector</a></td><td class="desc">This class is responsible for establishing contact with the XeThru module </td></tr>
<tr id="row_0_17_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a></td><td class="desc">Interface to the Xethru X2M200 Application module </td></tr>
<tr id="row_0_18_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a></td><td class="desc">C++ includes: PyX4M200.hpp </td></tr>
<tr id="row_0_19_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="desc">Proxy of C++ XeThru::PyX4M210 class </td></tr>
<tr id="row_0_20_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="desc">C++ includes: PyX4M300.hpp </td></tr>
<tr id="row_0_21_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="desc">C++ includes: PyXEP.hpp </td></tr>
<tr id="row_0_22_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_float_data.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.RadarBasebandFloatData</a></td><td class="desc"></td></tr>
<tr id="row_0_23_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_q15_data.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.RadarBasebandQ15Data</a></td><td class="desc"></td></tr>
<tr id="row_0_24_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_data.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.RadarRfData</a></td><td class="desc"></td></tr>
<tr id="row_0_25_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a></td><td class="desc"></td></tr>
<tr id="row_0_26_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a></td><td class="desc">The <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml" title="The RecordingOptions class allows specifying options for recording. ">RecordingOptions</a> class allows specifying options for recording </td></tr>
<tr id="row_0_27_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_respiration_data.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.RespirationData</a></td><td class="desc">Represents the respiration status data coming from the module </td></tr>
<tr id="row_0_28_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_data.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.SleepData</a></td><td class="desc">Represents the sleep status data coming from the module </td></tr>
<tr id="row_0_29_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_stage_data.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.SleepStageData</a></td><td class="desc"></td></tr>
<tr id="row_0_30_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_vital_signs_data.xhtml" target="_self">pymoduleconnector.moduleconnectorwrapper.VitalSignsData</a></td><td class="desc">Various vital signs </td></tr>
<tr id="row_1_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_1_" class="arrow" onclick="toggleFolder('1_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>Exception</b></td><td class="desc"></td></tr>
<tr id="row_1_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_regmap_error.xhtml" target="_self">pymoduleconnector.extras.regmap.RegmapError</a></td><td class="desc">Generic register map error </td></tr>
<tr id="row_2_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_2_" class="arrow" onclick="toggleFolder('2_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>object</b></td><td class="desc"></td></tr>
<tr id="row_2_0_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_2_0_" class="arrow" onclick="toggleFolder('2_0_')">&#9658;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg.xhtml" target="_self">pymoduleconnector.extras.regmap.Reg</a></td><td class="desc">Generic register class </td></tr>
<tr id="row_2_0_0_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_apc_dvdd_testmode.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.ApcDvddTestmode</a></td><td class="desc"></td></tr>
<tr id="row_2_0_1_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_apc_temp_trim.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.ApcTempTrim</a></td><td class="desc"></td></tr>
<tr id="row_2_0_2_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_avdd_rx_ctrl.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.AvddRxCtrl</a></td><td class="desc"></td></tr>
<tr id="row_2_0_3_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_avdd_testmode.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.AvddTestmode</a></td><td class="desc"></td></tr>
<tr id="row_2_0_4_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_avdd_tx_ctrl.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.AvddTxCtrl</a></td><td class="desc"></td></tr>
<tr id="row_2_0_5_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_boot_from_otp_pif.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.BootFromOtpPif</a></td><td class="desc"></td></tr>
<tr id="row_2_0_6_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_boot_from_otp_spi.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.BootFromOtpSpi</a></td><td class="desc"></td></tr>
<tr id="row_2_0_7_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_chip_id_dig.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.ChipIdDig</a></td><td class="desc"></td></tr>
<tr id="row_2_0_8_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_chip_id_sys.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.ChipIdSys</a></td><td class="desc"></td></tr>
<tr id="row_2_0_9_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_clkout_sel.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.ClkoutSel</a></td><td class="desc"></td></tr>
<tr id="row_2_0_10_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_ctrl1.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.CommonPllCtrl1</a></td><td class="desc"></td></tr>
<tr id="row_2_0_11_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_ctrl2.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.CommonPllCtrl2</a></td><td class="desc"></td></tr>
<tr id="row_2_0_12_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_ctrl3.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.CommonPllCtrl3</a></td><td class="desc"></td></tr>
<tr id="row_2_0_13_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_ctrl4.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.CommonPllCtrl4</a></td><td class="desc"></td></tr>
<tr id="row_2_0_14_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_frac0.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.CommonPllFrac0</a></td><td class="desc"></td></tr>
<tr id="row_2_0_15_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_frac1.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.CommonPllFrac1</a></td><td class="desc"></td></tr>
<tr id="row_2_0_16_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_frac2.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.CommonPllFrac2</a></td><td class="desc"></td></tr>
<tr id="row_2_0_17_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_cpu_reset.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.CpuReset</a></td><td class="desc"></td></tr>
<tr id="row_2_0_18_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_cpu_spi_master_clk_ctrl.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.CpuSpiMasterClkCtrl</a></td><td class="desc"></td></tr>
<tr id="row_2_0_19_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dac_anatestreq.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.DacAnatestreq</a></td><td class="desc"></td></tr>
<tr id="row_2_0_20_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dac_trim.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.DacTrim</a></td><td class="desc"></td></tr>
<tr id="row_2_0_21_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_debug.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.Debug</a></td><td class="desc"></td></tr>
<tr id="row_2_0_22_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_debug_xif.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.DebugXif</a></td><td class="desc"></td></tr>
<tr id="row_2_0_23_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dvdd_rx_ctrl.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.DvddRxCtrl</a></td><td class="desc"></td></tr>
<tr id="row_2_0_24_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dvdd_testmode.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.DvddTestmode</a></td><td class="desc"></td></tr>
<tr id="row_2_0_25_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dvdd_tx_ctrl.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.DvddTxCtrl</a></td><td class="desc"></td></tr>
<tr id="row_2_0_26_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_fetch_radar_data_pif.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.FetchRadarDataPif</a></td><td class="desc"></td></tr>
<tr id="row_2_0_27_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_fetch_radar_data_spi.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.FetchRadarDataSpi</a></td><td class="desc"></td></tr>
<tr id="row_2_0_28_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_firmware_version.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.FirmwareVersion</a></td><td class="desc"></td></tr>
<tr id="row_2_0_29_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_firmware_version_spi.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.FirmwareVersionSpi</a></td><td class="desc"></td></tr>
<tr id="row_2_0_30_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_force_one.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.ForceOne</a></td><td class="desc"></td></tr>
<tr id="row_2_0_31_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_force_zero.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.ForceZero</a></td><td class="desc"></td></tr>
<tr id="row_2_0_32_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_from_cpu_read_data.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.FromCpuReadData</a></td><td class="desc"></td></tr>
<tr id="row_2_0_33_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_from_cpu_write_data.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.FromCpuWriteData</a></td><td class="desc"></td></tr>
<tr id="row_2_0_34_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_from_mem_read_data.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.FromMemReadData</a></td><td class="desc"></td></tr>
<tr id="row_2_0_35_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_gpio_in.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.GpioIn</a></td><td class="desc"></td></tr>
<tr id="row_2_0_36_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_gpio_oe.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.GpioOe</a></td><td class="desc"></td></tr>
<tr id="row_2_0_37_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_gpio_out.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.GpioOut</a></td><td class="desc"></td></tr>
<tr id="row_2_0_38_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl1.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.IoCtrl1</a></td><td class="desc"></td></tr>
<tr id="row_2_0_39_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl2.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.IoCtrl2</a></td><td class="desc"></td></tr>
<tr id="row_2_0_40_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl3.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.IoCtrl3</a></td><td class="desc"></td></tr>
<tr id="row_2_0_41_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl4.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.IoCtrl4</a></td><td class="desc"></td></tr>
<tr id="row_2_0_42_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl5.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.IoCtrl5</a></td><td class="desc"></td></tr>
<tr id="row_2_0_43_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl6.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.IoCtrl6</a></td><td class="desc"></td></tr>
<tr id="row_2_0_44_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_iref_trim.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.IrefTrim</a></td><td class="desc"></td></tr>
<tr id="row_2_0_45_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_ldo_status1.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.LdoStatus1</a></td><td class="desc"></td></tr>
<tr id="row_2_0_46_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_ldo_status2.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.LdoStatus2</a></td><td class="desc"></td></tr>
<tr id="row_2_0_47_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_lna_anatestreq.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.LnaAnatestreq</a></td><td class="desc"></td></tr>
<tr id="row_2_0_48_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_lock_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.LockStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_49_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mclk_trx_backend_clk_ctrl.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.MclkTrxBackendClkCtrl</a></td><td class="desc"></td></tr>
<tr id="row_2_0_50_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mcu_bist_ctrl.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.McuBistCtrl</a></td><td class="desc"></td></tr>
<tr id="row_2_0_51_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mcu_bist_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.McuBistStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_52_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mem_first_addr_lsb.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.MemFirstAddrLsb</a></td><td class="desc"></td></tr>
<tr id="row_2_0_53_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mem_first_addr_msb.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.MemFirstAddrMsb</a></td><td class="desc"></td></tr>
<tr id="row_2_0_54_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mem_mode.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.MemMode</a></td><td class="desc"></td></tr>
<tr id="row_2_0_55_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_misc_ctrl.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.MiscCtrl</a></td><td class="desc"></td></tr>
<tr id="row_2_0_56_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_osc_ctrl.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.OscCtrl</a></td><td class="desc"></td></tr>
<tr id="row_2_0_57_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_otp_ctrl.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.OtpCtrl</a></td><td class="desc"></td></tr>
<tr id="row_2_0_58_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_mb_clear_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.PifMbClearStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_59_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_mb_fifo_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.PifMbFifoStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_60_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_mem_clear_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.PifMemClearStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_61_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_mem_fifo_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.PifMemFifoStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_62_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data0_clear_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.PifRadarData0ClearStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_63_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data0_fifo_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.PifRadarData0FifoStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_64_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data1_clear_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.PifRadarData1ClearStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_65_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data1_fifo_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.PifRadarData1FifoStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_66_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data_clear_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.PifRadarDataClearStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_67_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_preamp_trim.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.PreampTrim</a></td><td class="desc"></td></tr>
<tr id="row_2_0_68_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_bist_ctrl.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RadarBistCtrl</a></td><td class="desc"></td></tr>
<tr id="row_2_0_69_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_bist_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RadarBistStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_70_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_data_pif.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RadarDataPif</a></td><td class="desc"></td></tr>
<tr id="row_2_0_71_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_data_pif_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RadarDataPifStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_72_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_data_spi.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RadarDataSpi</a></td><td class="desc"></td></tr>
<tr id="row_2_0_73_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_data_spi_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RadarDataSpiStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_74_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_readout_idle.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RadarReadoutIdle</a></td><td class="desc"></td></tr>
<tr id="row_2_0_75_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_ram_select.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RamSelect</a></td><td class="desc"></td></tr>
<tr id="row_2_0_76_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_counter_lsb.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxCounterLsb</a></td><td class="desc"></td></tr>
<tr id="row_2_0_77_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_counter_num_bytes.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxCounterNumBytes</a></td><td class="desc"></td></tr>
<tr id="row_2_0_78_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_downconversion_coeff_i1.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxDownconversionCoeffI1</a></td><td class="desc"></td></tr>
<tr id="row_2_0_79_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_downconversion_coeff_i2.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxDownconversionCoeffI2</a></td><td class="desc"></td></tr>
<tr id="row_2_0_80_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_downconversion_coeff_q1.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxDownconversionCoeffQ1</a></td><td class="desc"></td></tr>
<tr id="row_2_0_81_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_downconversion_coeff_q2.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxDownconversionCoeffQ2</a></td><td class="desc"></td></tr>
<tr id="row_2_0_82_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_fe_anatestreq.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxFeAnatestreq</a></td><td class="desc"></td></tr>
<tr id="row_2_0_83_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_mframes.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxMframes</a></td><td class="desc"></td></tr>
<tr id="row_2_0_84_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_mframes_coarse.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxMframesCoarse</a></td><td class="desc"></td></tr>
<tr id="row_2_0_85_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_ctrl1.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxPllCtrl1</a></td><td class="desc"></td></tr>
<tr id="row_2_0_86_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_ctrl2.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxPllCtrl2</a></td><td class="desc"></td></tr>
<tr id="row_2_0_87_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_skewcalin.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxPllSkewcalin</a></td><td class="desc"></td></tr>
<tr id="row_2_0_88_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_skew_ctrl.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxPllSkewCtrl</a></td><td class="desc"></td></tr>
<tr id="row_2_0_89_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxPllStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_90_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_ram_line_first_msb.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxRamLineFirstMsb</a></td><td class="desc"></td></tr>
<tr id="row_2_0_91_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_ram_line_last_msb.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxRamLineLastMsb</a></td><td class="desc"></td></tr>
<tr id="row_2_0_92_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_ram_lsbs.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxRamLsbs</a></td><td class="desc"></td></tr>
<tr id="row_2_0_93_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_ram_write_offset_msb.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxRamWriteOffsetMsb</a></td><td class="desc"></td></tr>
<tr id="row_2_0_94_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_reset_counters.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxResetCounters</a></td><td class="desc"></td></tr>
<tr id="row_2_0_95_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_wait.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.RxWait</a></td><td class="desc"></td></tr>
<tr id="row_2_0_96_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_sampler_preset_lsb.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SamplerPresetLsb</a></td><td class="desc"></td></tr>
<tr id="row_2_0_97_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_sampler_preset_msb.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SamplerPresetMsb</a></td><td class="desc"></td></tr>
<tr id="row_2_0_98_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_smpl_mode.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SmplMode</a></td><td class="desc"></td></tr>
<tr id="row_2_0_99_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_config.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiConfig</a></td><td class="desc"></td></tr>
<tr id="row_2_0_100_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_config_pif.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiConfigPif</a></td><td class="desc"></td></tr>
<tr id="row_2_0_101_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_idle.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiMasterIdle</a></td><td class="desc"></td></tr>
<tr id="row_2_0_102_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_mode.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiMasterMode</a></td><td class="desc"></td></tr>
<tr id="row_2_0_103_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_radar_burst_kick.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiMasterRadarBurstKick</a></td><td class="desc"></td></tr>
<tr id="row_2_0_104_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_radar_burst_size_lsb.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiMasterRadarBurstSizeLsb</a></td><td class="desc"></td></tr>
<tr id="row_2_0_105_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_send.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiMasterSend</a></td><td class="desc"></td></tr>
<tr id="row_2_0_106_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mb_clear_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiMbClearStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_107_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mb_fifo_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiMbFifoStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_108_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mem_clear_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiMemClearStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_109_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mem_fifo_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiMemFifoStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_110_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data0_clear_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiRadarData0ClearStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_111_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data0_fifo_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiRadarData0FifoStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_112_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data1_clear_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiRadarData1ClearStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_113_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data1_fifo_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiRadarData1FifoStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_114_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data_clear_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SpiRadarDataClearStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_115_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_to_cpu_read_data.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.ToCpuReadData</a></td><td class="desc"></td></tr>
<tr id="row_2_0_116_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_to_cpu_write_data.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.ToCpuWriteData</a></td><td class="desc"></td></tr>
<tr id="row_2_0_117_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_to_mem_write_data.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.ToMemWriteData</a></td><td class="desc"></td></tr>
<tr id="row_2_0_118_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_backend_done.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxBackendDone</a></td><td class="desc"></td></tr>
<tr id="row_2_0_119_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_clocks_per_pulse.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxClocksPerPulse</a></td><td class="desc"></td></tr>
<tr id="row_2_0_120_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_ctrl_done.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxCtrlDone</a></td><td class="desc"></td></tr>
<tr id="row_2_0_121_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_ctrl_mode.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxCtrlMode</a></td><td class="desc"></td></tr>
<tr id="row_2_0_122_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_max_h.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxDacMaxH</a></td><td class="desc"></td></tr>
<tr id="row_2_0_123_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_max_l.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxDacMaxL</a></td><td class="desc"></td></tr>
<tr id="row_2_0_124_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_min_h.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxDacMinH</a></td><td class="desc"></td></tr>
<tr id="row_2_0_125_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_min_l.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxDacMinL</a></td><td class="desc"></td></tr>
<tr id="row_2_0_126_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_override_h.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxDacOverrideH</a></td><td class="desc"></td></tr>
<tr id="row_2_0_127_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_override_l.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxDacOverrideL</a></td><td class="desc"></td></tr>
<tr id="row_2_0_128_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_override_load.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxDacOverrideLoad</a></td><td class="desc"></td></tr>
<tr id="row_2_0_129_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_step.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxDacStep</a></td><td class="desc"></td></tr>
<tr id="row_2_0_130_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_iterations.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxIterations</a></td><td class="desc"></td></tr>
<tr id="row_2_0_131_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_lfsr_reset.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxLfsrReset</a></td><td class="desc"></td></tr>
<tr id="row_2_0_132_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_lfsr_taps0.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxLfsrTaps0</a></td><td class="desc"></td></tr>
<tr id="row_2_0_133_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_lfsr_taps1.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxLfsrTaps1</a></td><td class="desc"></td></tr>
<tr id="row_2_0_134_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_lfsr_taps2.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxLfsrTaps2</a></td><td class="desc"></td></tr>
<tr id="row_2_0_135_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_pulses_per_step_lsb.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxPulsesPerStepLsb</a></td><td class="desc"></td></tr>
<tr id="row_2_0_136_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_pulses_per_step_msb.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxPulsesPerStepMsb</a></td><td class="desc"></td></tr>
<tr id="row_2_0_137_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_start.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TrxStart</a></td><td class="desc"></td></tr>
<tr id="row_2_0_138_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_ctrl1.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TxPllCtrl1</a></td><td class="desc"></td></tr>
<tr id="row_2_0_139_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_ctrl2.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TxPllCtrl2</a></td><td class="desc"></td></tr>
<tr id="row_2_0_140_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_skewcalin.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TxPllSkewcalin</a></td><td class="desc"></td></tr>
<tr id="row_2_0_141_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_skew_ctrl.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TxPllSkewCtrl</a></td><td class="desc"></td></tr>
<tr id="row_2_0_142_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_status.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TxPllStatus</a></td><td class="desc"></td></tr>
<tr id="row_2_0_143_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_wait.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.TxWait</a></td><td class="desc"></td></tr>
<tr id="row_2_0_144_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_vref_trim.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.VrefTrim</a></td><td class="desc"></td></tr>
<tr id="row_2_1_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_2_1_" class="arrow" onclick="toggleFolder('2_1_')">&#9658;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_block.xhtml" target="_self">pymoduleconnector.extras.regmap.RegBlock</a></td><td class="desc">The <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_block.xhtml" title="The RegBlock class encapsulates one or more RegMap objects (register maps). ">RegBlock</a> class encapsulates one or more <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_map.xhtml">RegMap</a> objects (register maps) </td></tr>
<tr id="row_2_1_0_" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_x4.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.X4</a></td><td class="desc"></td></tr>
<tr id="row_2_2_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_2_2_" class="arrow" onclick="toggleFolder('2_2_')">&#9658;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_map.xhtml" target="_self">pymoduleconnector.extras.regmap.RegMap</a></td><td class="desc"></td></tr>
<tr id="row_2_2_0_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_p_i_f.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.PIF</a></td><td class="desc"></td></tr>
<tr id="row_2_2_1_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_s_p_i.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.SPI</a></td><td class="desc"></td></tr>
<tr id="row_2_2_2_" class="even" style="display:none;"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_x_i_f.xhtml" target="_self">pymoduleconnector.extras.x4_regmap_autogen.XIF</a></td><td class="desc"></td></tr>
<tr id="row_2_3_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_segment.xhtml" target="_self">pymoduleconnector.extras.regmap.RegSegment</a></td><td class="desc">Register segment class </td></tr>
<tr id="row_3_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_3_" class="arrow" onclick="toggleFolder('3_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>PyDataPlayer</b></td><td class="desc"></td></tr>
<tr id="row_3_0_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnector_1_1_data_player.xhtml" target="_self">pymoduleconnector.moduleconnector.DataPlayer</a></td><td class="desc">Inherits <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_player.xhtml">pymoduleconnector.moduleconnectorwrapper.PyDataPlayer</a> </td></tr>
<tr id="row_4_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_4_" class="arrow" onclick="toggleFolder('4_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>PyDataReader</b></td><td class="desc"></td></tr>
<tr id="row_4_0_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnector_1_1_data_reader.xhtml" target="_self">pymoduleconnector.moduleconnector.DataReader</a></td><td class="desc">Inherits <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml" title="The PyDataReader class allows reading of xethru data records from a recording. ">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a> </td></tr>
<tr id="row_5_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_5_" class="arrow" onclick="toggleFolder('5_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>PyDataRecorder</b></td><td class="desc"></td></tr>
<tr id="row_5_0_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnector_1_1_data_recorder.xhtml" target="_self">pymoduleconnector.moduleconnector.DataRecorder</a></td><td class="desc">Inherits <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_recorder.xhtml" title="The DataRecorder class allows recording of xethru data types. ">pymoduleconnector.moduleconnectorwrapper.PyDataRecorder</a> </td></tr>
<tr id="row_6_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_6_" class="arrow" onclick="toggleFolder('6_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>PythonModuleConnector</b></td><td class="desc"></td></tr>
<tr id="row_6_0_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml" target="_self">pymoduleconnector.moduleconnector.ModuleConnector</a></td><td class="desc">Inherits <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml" title="This class is responsible for establishing contact with the XeThru module. ">pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector</a> </td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
