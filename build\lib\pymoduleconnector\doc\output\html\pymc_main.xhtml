<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">pymoduleconnector </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>The python library pymoduleconnector wraps the ModuleConnector C++ library. The main difference between the libraries are that the python wrapper returns a data object and throws exceptions, while the C++ uses output parameters for the output data and returns a SUCCESS or an error code.</p>
<p>See the ModuleConnector documentation for details.</p>
<h2>Installation</h2>
<p>An installation script is provided and the pymoduleconnector package is installed by running the following command from the pymoduleconnector root directory: </p><pre class="fragment">$ python setup.py install
</pre><h3>Prerequisites</h3>
<ul>
<li>Python 2 or 3 on 32/64 bit Windows, Raspbian 8 (jessie), x86_64 Ubuntu 16.04 (glibcxx &gt;=3.4.20), or OSX</li>
<li>Setuptools</li>
</ul>
<p>Other versions than the listed may also work but have not been tested.</p>
<p>If you are unsure of how to install python, we recommend to install the python distribution package anaconda from continuum: <a href="https://www.continuum.io/downloads">https://www.continuum.io/downloads</a></p>
<h2>Getting Started</h2>
<p>All ModuleConnector functionality is available through the <a class="el" href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml">pymoduleconnector.moduleconnector.ModuleConnector</a> class which gives access to the different interfaces supported by a module.</p>
<p>The following interfaces are supported by the ModuleConnector python wrapper:</p>
<ul>
<li><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a></li>
<li><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></li>
<li><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a></li>
<li><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></li>
</ul>
<h3>Connecting to a Module</h3>
<p>Connects to an X2M200 sensor, sends a ping command and prints the response:</p>
<div class="fragment"><div class="line"><span class="keyword">from</span> pymoduleconnector <span class="keyword">import</span> ModuleConnector</div><div class="line">device_name = <span class="stringliteral">&quot;COM4&quot;</span> <span class="comment"># The module device name.</span></div><div class="line">mc = ModuleConnector(device_name, log_level = 0)</div><div class="line">x2m200 = mc.get_x2m200()</div><div class="line">x2m200.set_sensor_mode_idle()</div><div class="line">pong = x2m200.ping()</div><div class="line"><span class="keywordflow">print</span> <span class="stringliteral">&quot;Received pong, value is:&quot;</span>,  hex(pong)</div></div><!-- fragment --><p>Methods throw a RuntimeException if they fail.</p>
<h2>Examples</h2>
<ul>
<li><a class="el" href="_x4_m300_printout_presence_state_8py-example.xhtml">X4M300_printout_presence_state.py</a> Connect to a X4M300 and print presence single frames.</li>
<li><a class="el" href="_x2_m200_plot_respiration_8py-example.xhtml">X2M200_plot_respiration.py</a> Connect to a X2M200 module and plot the respiration message output.</li>
<li><a class="el" href="_x2_m200_record_8py-example.xhtml">X2M200_record.py</a> How to use the DataRecorder.</li>
<li><a class="el" href="_x2_m200_read_record_8py-example.xhtml">X2M200_read_record.py</a> How to use the DataReader to read back previously recorded data. </li>
</ul>
</div></div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
