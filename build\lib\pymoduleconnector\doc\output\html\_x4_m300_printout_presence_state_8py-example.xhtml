<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: X4M300_printout_presence_state.py</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">X4M300_printout_presence_state.py</div>  </div>
</div><!--header-->
<div class="contents">
<p>Target module: X4M300Introduction: This is an example of how to set up and read presence single messages from the X4M300 module with the ModuleConnector python wrapper.</p>
<p>Command to run: "python X4M300_printout_presence_state.py -d com8" or "python3 X4M300_printout_presence_state.py -d com8" change "com8" with your device name, using "--help" to see other options. Using TCP server address as device name is also supported, e.g. "python X4M200_sleep_record.py -d tcp://*************:3000".</p>
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">#!/usr/bin/env python</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="stringliteral">&quot;&quot;&quot; \example X4M300_printout_presence_state.py</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="stringliteral">#Target module: X4M300</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="stringliteral">#Introduction: This is an example of how to set up and read presence single messages from the</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="stringliteral">               X4M300 module with the ModuleConnector python wrapper.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="stringliteral">#Command to run: &quot;python X4M300_printout_presence_state.py -d com8&quot; or &quot;python3 X4M300_printout_presence_state.py -d com8&quot;</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="stringliteral">                 change &quot;com8&quot; with your device name, using &quot;--help&quot; to see other options.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="stringliteral">                 Using TCP server address as device name is also supported, e.g. </span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="stringliteral">                 &quot;python X4M200_sleep_record.py -d tcp://*************:3000&quot;.</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="stringliteral">          </span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="stringliteral">&quot;&quot;&quot;</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="keyword">from</span> __future__ <span class="keyword">import</span> print_function</div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="keyword">import</span> numpy <span class="keyword">as</span> np</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="keyword">import</span> time</div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="keyword">from</span> pymoduleconnector <span class="keyword">import</span> ModuleConnector</div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="keyword">from</span> <a class="code" href="namespacepymoduleconnector_1_1ids.xhtml">pymoduleconnector.ids</a> <span class="keyword">import</span> *</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="keyword">from</span> time <span class="keyword">import</span> sleep</div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;</div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="keyword">def </span>x4m300_presence_simpleoutput(device_name, detection_zone=(0.5,9), sensitivity=5, num_messages=0):</div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;</div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;    <span class="comment"># User settings</span></div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;    detzone_start = detection_zone[0]</div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;    detzone_end = detection_zone[1]</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;    presence_state_text = []</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;    presence_state_text.append(<span class="stringliteral">&quot;No presence&quot;</span>)</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;    presence_state_text.append(<span class="stringliteral">&quot;Presence&quot;</span>)</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;    presence_state_text.append(<span class="stringliteral">&quot;Initializing&quot;</span>)</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;    mc = ModuleConnector(device_name, log_level=0)</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;    x4m300 = mc.get_x4m300()</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;    sleep(1) <span class="comment"># Allow for MC to read waiting messages from module.</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;    <span class="keywordflow">try</span>:</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;        x4m300.set_sensor_mode(XTID_SM_STOP, 0) <span class="comment"># Make sure no profile is running.</span></div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        print(<span class="stringliteral">&quot;Stopped already running profile.&quot;</span>)</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;    <span class="keywordflow">except</span> RuntimeError:</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        <span class="comment"># If not initialized, stop returns error. Still OK, just wanted to make sure the profile was not running.</span></div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;        <span class="keywordflow">pass</span></div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;    <span class="comment"># Now flush old messages from module</span></div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;    print(<span class="stringliteral">&quot;Flushing any old data.&quot;</span>)</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;    <span class="keywordflow">while</span> x4m300.peek_message_presence_single():</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        presence_single = x4m300.read_message_presence_single()</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;    <span class="comment"># Read module info</span></div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;    print(<span class="stringliteral">&quot;FirmwareID:&quot;</span>, x4m300.get_system_info(XTID_SSIC_FIRMWAREID))</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;    print(<span class="stringliteral">&quot;Version:&quot;</span>, x4m300.get_system_info(XTID_SSIC_VERSION))</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;    print(<span class="stringliteral">&quot;Build:&quot;</span>, x4m300.get_system_info(XTID_SSIC_BUILD))</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;    print(<span class="stringliteral">&quot;Serial number:&quot;</span>, x4m300.get_system_info(XTID_SSIC_SERIALNUMBER))</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;    print(<span class="stringliteral">&quot;Loading new profile.&quot;</span>)</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;    x4m300.load_profile(XTS_ID_APP_PRESENCE_2)</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;    print(<span class="stringliteral">&quot;Selecting module output.&quot;</span>)</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;    x4m300.set_output_control(XTS_ID_PRESENCE_SINGLE, 1) <span class="comment"># PresenceSingle</span></div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;    x4m300.set_output_control(XTS_ID_PRESENCE_MOVINGLIST, 0) <span class="comment"># PresenceMovingList</span></div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;    print(<span class="stringliteral">&quot;Setting user settings: DetectionZone = &quot;</span> + str(detzone_start) + <span class="stringliteral">&quot; to &quot;</span> + str(detzone_end) + <span class="stringliteral">&quot;, Sensitivity = &quot;</span> + str(sensitivity))</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;    x4m300.set_detection_zone(detzone_start, detzone_end)</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;    x4m300.set_sensitivity(sensitivity)</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;    print(<span class="stringliteral">&quot;Start profile execution.&quot;</span>)</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;    x4m300.set_sensor_mode(XTID_SM_RUN, 0) <span class="comment"># Make sure no profile is running.</span></div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;    print(<span class="stringliteral">&quot;Waiting for data...&quot;</span>)</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;    n = 0</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;    <span class="keywordflow">while</span> num_messages == 0 <span class="keywordflow">or</span> n &lt; num_messages:</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        time.sleep(0.1)</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;        <span class="keywordflow">while</span> x4m300.peek_message_presence_single():</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;            presence_single = x4m300.read_message_presence_single()</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;            print(<span class="stringliteral">&quot;Presence -&gt;&quot;</span></div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;                + <span class="stringliteral">&quot; FrameCounter: &quot;</span> + str(presence_single.frame_counter)</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;                + <span class="stringliteral">&quot;, State: &quot;</span> + presence_state_text[presence_single.presence_state]</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                + <span class="stringliteral">&quot;, Distance: &quot;</span> + str(round(presence_single.distance,2))</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;                + <span class="stringliteral">&quot;, SignalQuality: &quot;</span> + str(presence_single.signal_quality)</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;                )</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;            n += 1</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;    x4m300.set_sensor_mode(XTID_SM_STOP, 0)</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;<span class="keyword">def </span>main():</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;    <span class="keyword">import</span> sys</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;    <span class="keyword">from</span> optparse <span class="keyword">import</span> OptionParser</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    parser = OptionParser()</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;    parser.add_option(</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;        <span class="stringliteral">&quot;-d&quot;</span>,</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;        <span class="stringliteral">&quot;--device&quot;</span>,</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        dest=<span class="stringliteral">&quot;device_name&quot;</span>,</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;        help=<span class="stringliteral">&quot;device file to use, example: python %s -d COM4&quot;</span>%sys.argv[0],</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;        metavar=<span class="stringliteral">&quot;FILE&quot;</span>)</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;    parser.add_option(</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;        <span class="stringliteral">&quot;-n&quot;</span>,</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        <span class="stringliteral">&quot;--num-messages&quot;</span>,</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        dest=<span class="stringliteral">&quot;num_messages&quot;</span>,</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;        type=int,</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        default=0,</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;        help=<span class="stringliteral">&quot;how many messages to read (0 = infinite)&quot;</span>,</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;        metavar=<span class="stringliteral">&quot;INT&quot;</span>)</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;    parser.add_option(<span class="stringliteral">&#39;-z&#39;</span>, <span class="stringliteral">&#39;--detection_zone&#39;</span>, nargs=2, type=<span class="stringliteral">&#39;float&#39;</span>,</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;        help=<span class="stringliteral">&#39;Start and stop of detection zone.&#39;</span>, metavar=<span class="stringliteral">&#39;START STOP&#39;</span>,</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;        default=(0.5, 9))</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;    parser.add_option(<span class="stringliteral">&#39;-s&#39;</span>, <span class="stringliteral">&#39;--sensitivity&#39;</span>, nargs=1, type=<span class="stringliteral">&#39;int&#39;</span>,</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;        help=<span class="stringliteral">&#39;Sensor Sensitivity.&#39;</span>, metavar=<span class="stringliteral">&#39;SENSITIVITY&#39;</span>,</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;        default=5)</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;    (options, args) = parser.parse_args()</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;    <span class="keywordflow">if</span> <span class="keywordflow">not</span> options.device_name:</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;        print(<span class="stringliteral">&quot;Please specify a device name, example: python %s -d COM4&quot;</span>%sys.argv[0])</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;        sys.exit(1)</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;    x4m300_presence_simpleoutput(**vars(options))</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;<span class="keywordflow">if</span> __name__ == <span class="stringliteral">&quot;__main__&quot;</span>:</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;    main()</div></div><!-- fragment --> </div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
