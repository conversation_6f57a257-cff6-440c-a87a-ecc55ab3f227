<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: XEP_X4M200_X4M300_access_registers.py</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">XEP_X4M200_X4M300_access_registers.py</div>  </div>
</div><!--header-->
<div class="contents">
<p>Target module: X4M200,X4M300,X4M03Introduction: This is an example of how to use the XEP interface from python with the regmap class for easy access to chip registers.</p>
<p>Command to run: "python XEP_X4M200_X4M300_access_registers.py" or "python3 XEP_X4M200_X4M300_access_registers.py"</p>
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">#!/usr/bin/env python</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"># -*- coding: utf-8 -*-</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="stringliteral">&quot;&quot;&quot; \example XEP_X4M200_X4M300_access_registers.py</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="stringliteral">#Target module: X4M200,X4M300,X4M03</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="stringliteral">#Introduction: This is an example of how to use the XEP interface from python with the regmap class for easy access to chip registers.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="stringliteral">#Command to run: &quot;python XEP_X4M200_X4M300_access_registers.py&quot; or &quot;python3 XEP_X4M200_X4M300_access_registers.py&quot;</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="stringliteral">&quot;&quot;&quot;</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;</div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="keyword">from</span> __future__ <span class="keyword">import</span> print_function</div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="keyword">from</span> pymoduleconnector <span class="keyword">import</span> ModuleConnector</div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="keyword">from</span> <a class="code" href="namespacepymoduleconnector_1_1extras_1_1x4__regmap__autogen.xhtml">pymoduleconnector.extras.x4_regmap_autogen</a> <span class="keyword">import</span> X4</div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="keyword">from</span> <a class="code" href="namespacepymoduleconnector_1_1extras_1_1auto.xhtml">pymoduleconnector.extras.auto</a> <span class="keyword">import</span> auto</div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="keyword">import</span> pymoduleconnector</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="keyword">import</span> sys</div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="keyword">import</span> time</div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="keyword">def </span>main():</div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;    module = auto(<span class="stringliteral">&#39;x4&#39;</span>)</div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;    mc = ModuleConnector(module[0])</div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;    xep = mc.get_xep()</div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;    regmap = X4(xep)</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;    <span class="comment"># Use regmap object to access registers, for example to set tx_power</span></div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;    print(<span class="stringliteral">&quot;Setting TX power to 1&quot;</span>)</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;    regmap.tx_power = 1</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;    print(<span class="stringliteral">&quot;TX power is now %d&quot;</span> % regmap.tx_power)</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;    <span class="comment"># In an interactive session use tab-completion to get a list of</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;    <span class="comment"># available registers</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;    <span class="keywordflow">return</span> 0</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="keywordflow">if</span> __name__ == <span class="stringliteral">&quot;__main__&quot;</span>:</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;    sys.exit(main())</div></div><!-- fragment --> </div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
