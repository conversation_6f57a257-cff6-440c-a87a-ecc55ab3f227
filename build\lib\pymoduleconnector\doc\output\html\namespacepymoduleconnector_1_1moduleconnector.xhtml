<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnector Namespace Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><a class="el" href="namespacepymoduleconnector_1_1moduleconnector.xhtml">moduleconnector</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnector Namespace Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Entrypoint functionality.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><b>Bootloader</b></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inherits pymoduleconnector.moduleconnectorwrapper.Bootloader. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnector_1_1_data_player.xhtml">DataPlayer</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inherits <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_player.xhtml">pymoduleconnector.moduleconnectorwrapper.PyDataPlayer</a>.  <a href="classpymoduleconnector_1_1moduleconnector_1_1_data_player.xhtml#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnector_1_1_data_reader.xhtml">DataReader</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inherits <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml" title="The PyDataReader class allows reading of xethru data records from a recording. ">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a>.  <a href="classpymoduleconnector_1_1moduleconnector_1_1_data_reader.xhtml#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnector_1_1_data_recorder.xhtml">DataRecorder</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inherits <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_recorder.xhtml" title="The DataRecorder class allows recording of xethru data types. ">pymoduleconnector.moduleconnectorwrapper.PyDataRecorder</a>.  <a href="classpymoduleconnector_1_1moduleconnector_1_1_data_recorder.xhtml#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml">ModuleConnector</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inherits <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml" title="This class is responsible for establishing contact with the XeThru module. ">pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector</a>.  <a href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ad56b7daa5d0bcd52f411b5bbb7e77e68"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacepymoduleconnector_1_1moduleconnector.xhtml#ad56b7daa5d0bcd52f411b5bbb7e77e68">create_mc</a> (args, kwargs)</td></tr>
<tr class="memdesc:ad56b7daa5d0bcd52f411b5bbb7e77e68"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initiate a context managed <a class="el" href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml" title="Inherits pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector. ">ModuleConnector</a> object.  <a href="#ad56b7daa5d0bcd52f411b5bbb7e77e68">More...</a><br /></td></tr>
<tr class="separator:ad56b7daa5d0bcd52f411b5bbb7e77e68"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Entrypoint functionality. </p>
</div><h2 class="groupheader">Function Documentation</h2>
<a id="ad56b7daa5d0bcd52f411b5bbb7e77e68"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad56b7daa5d0bcd52f411b5bbb7e77e68">&sect;&nbsp;</a></span>create_mc()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnector.create_mc </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>args</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>kwargs</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Initiate a context managed <a class="el" href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml" title="Inherits pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector. ">ModuleConnector</a> object. </p>
<p>Convenience function to get a context managed <a class="el" href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml" title="Inherits pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector. ">ModuleConnector</a> object.</p>
<p>All references to the object is deleted, thus the serial port connection is closed.</p>
<p><b>Examples</b> </p><div class="fragment"><div class="line">&gt;&gt;&gt; <span class="keyword">from</span> pymoduleconnector <span class="keyword">import</span> create_mc</div><div class="line">&gt;&gt;&gt; with create_mc(<span class="stringliteral">&#39;com11&#39;</span>) <span class="keyword">as</span> mc:</div><div class="line">&gt;&gt;&gt;     print(hex(mc.get_x2m200().ping()))</div><div class="line">0xaaeeaeeaL</div></div><!-- fragment --> 
</div>
</div>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
