<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnectorwrapper.RecordingOptions Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml">RecordingOptions</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.RecordingOptions Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>The <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml" title="The RecordingOptions class allows specifying options for recording. ">RecordingOptions</a> class allows specifying options for recording.  
 <a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a72a2df27e9a87987f8919a6288923f9f"><td class="memItemLeft" align="right" valign="top"><a id="a72a2df27e9a87987f8919a6288923f9f"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>__init__</b> (self, args)</td></tr>
<tr class="separator:a72a2df27e9a87987f8919a6288923f9f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3748f23da0a1e6582e7ac54ec11c400d"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a3748f23da0a1e6582e7ac54ec11c400d">set_session_id</a> (self, id)</td></tr>
<tr class="memdesc:a3748f23da0a1e6582e7ac54ec11c400d"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_session_id(RecordingOptions self, std::string const &amp; id)  <a href="#a3748f23da0a1e6582e7ac54ec11c400d">More...</a><br /></td></tr>
<tr class="separator:a3748f23da0a1e6582e7ac54ec11c400d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44620f5f8a32092b863d78e6baac9ad0"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a44620f5f8a32092b863d78e6baac9ad0">get_session_id</a> (self)</td></tr>
<tr class="memdesc:a44620f5f8a32092b863d78e6baac9ad0"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_session_id(RecordingOptions self) -&gt; std::string  <a href="#a44620f5f8a32092b863d78e6baac9ad0">More...</a><br /></td></tr>
<tr class="separator:a44620f5f8a32092b863d78e6baac9ad0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad0c2444e6629f7334ae3859ad6946d4b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#ad0c2444e6629f7334ae3859ad6946d4b">set_file_split_size</a> (self, size)</td></tr>
<tr class="memdesc:ad0c2444e6629f7334ae3859ad6946d4b"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_file_split_size(RecordingOptions self, PreferredSplitSize size)  <a href="#ad0c2444e6629f7334ae3859ad6946d4b">More...</a><br /></td></tr>
<tr class="separator:ad0c2444e6629f7334ae3859ad6946d4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a36c3a4834bf15f78e57918e318086d4a"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a36c3a4834bf15f78e57918e318086d4a">get_file_split_size</a> (self)</td></tr>
<tr class="memdesc:a36c3a4834bf15f78e57918e318086d4a"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_file_split_size(RecordingOptions self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml" title="The PreferredSplitSize class allows specifying a split size. ">PreferredSplitSize</a>  <a href="#a36c3a4834bf15f78e57918e318086d4a">More...</a><br /></td></tr>
<tr class="separator:a36c3a4834bf15f78e57918e318086d4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acf02bee9c4142297511c556d635305d0"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#acf02bee9c4142297511c556d635305d0">set_directory_split_size</a> (self, size)</td></tr>
<tr class="memdesc:acf02bee9c4142297511c556d635305d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_directory_split_size(RecordingOptions self, PreferredSplitSize size)  <a href="#acf02bee9c4142297511c556d635305d0">More...</a><br /></td></tr>
<tr class="separator:acf02bee9c4142297511c556d635305d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6217bf22e4808de72736154fc99c54fd"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a6217bf22e4808de72736154fc99c54fd">get_directory_split_size</a> (self)</td></tr>
<tr class="memdesc:a6217bf22e4808de72736154fc99c54fd"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_directory_split_size(RecordingOptions self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml" title="The PreferredSplitSize class allows specifying a split size. ">PreferredSplitSize</a>  <a href="#a6217bf22e4808de72736154fc99c54fd">More...</a><br /></td></tr>
<tr class="separator:a6217bf22e4808de72736154fc99c54fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f5788127ec56ee7d14ab848299988cb"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a5f5788127ec56ee7d14ab848299988cb">set_data_rate_limit</a> (self, limit)</td></tr>
<tr class="memdesc:a5f5788127ec56ee7d14ab848299988cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_data_rate_limit(RecordingOptions self, int limit)  <a href="#a5f5788127ec56ee7d14ab848299988cb">More...</a><br /></td></tr>
<tr class="separator:a5f5788127ec56ee7d14ab848299988cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a20fa9eabb0ed9ec8147d44738848854f"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a20fa9eabb0ed9ec8147d44738848854f">get_data_rate_limit</a> (self)</td></tr>
<tr class="memdesc:a20fa9eabb0ed9ec8147d44738848854f"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_data_rate_limit(RecordingOptions self) -&gt; int  <a href="#a20fa9eabb0ed9ec8147d44738848854f">More...</a><br /></td></tr>
<tr class="separator:a20fa9eabb0ed9ec8147d44738848854f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad1e010d865745ded5018f4dc3b70c6cf"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#ad1e010d865745ded5018f4dc3b70c6cf">set_user_header</a> (self, header)</td></tr>
<tr class="memdesc:ad1e010d865745ded5018f4dc3b70c6cf"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_user_header(RecordingOptions self, ucVector header)  <a href="#ad1e010d865745ded5018f4dc3b70c6cf">More...</a><br /></td></tr>
<tr class="separator:ad1e010d865745ded5018f4dc3b70c6cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a115bcb784f8909992cff589ec005722e"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a115bcb784f8909992cff589ec005722e">get_user_header</a> (self)</td></tr>
<tr class="memdesc:a115bcb784f8909992cff589ec005722e"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_user_header(RecordingOptions self) -&gt; ucVector  <a href="#a115bcb784f8909992cff589ec005722e">More...</a><br /></td></tr>
<tr class="separator:a115bcb784f8909992cff589ec005722e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed473a5de7c674635120149aea07e3de"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#aed473a5de7c674635120149aea07e3de">set_flush_on_write</a> (self, do_flush)</td></tr>
<tr class="memdesc:aed473a5de7c674635120149aea07e3de"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_flush_on_write(RecordingOptions self, bool do_flush)  <a href="#aed473a5de7c674635120149aea07e3de">More...</a><br /></td></tr>
<tr class="separator:aed473a5de7c674635120149aea07e3de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7df54e8220e70427c10478887935a096"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a7df54e8220e70427c10478887935a096">get_flush_on_write</a> (self)</td></tr>
<tr class="memdesc:a7df54e8220e70427c10478887935a096"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_flush_on_write(RecordingOptions self) -&gt; bool  <a href="#a7df54e8220e70427c10478887935a096">More...</a><br /></td></tr>
<tr class="separator:a7df54e8220e70427c10478887935a096"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a10d305ef3c0081c67e233ce16f36ba43"><td class="memItemLeft" align="right" valign="top"><a id="a10d305ef3c0081c67e233ce16f36ba43"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>this</b></td></tr>
<tr class="separator:a10d305ef3c0081c67e233ce16f36ba43"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml" title="The RecordingOptions class allows specifying options for recording. ">RecordingOptions</a> class allows specifying options for recording. </p>
<p>The <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml" title="The RecordingOptions class allows specifying options for recording. ">RecordingOptions</a> class contains more advance options for recording. It can be used to specify things like splitting of files and directories.</p>
<p>See DataRecorder</p>
<p>C++ includes: RecordingOptions.hpp </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a20fa9eabb0ed9ec8147d44738848854f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a20fa9eabb0ed9ec8147d44738848854f">&sect;&nbsp;</a></span>get_data_rate_limit()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RecordingOptions.get_data_rate_limit </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_data_rate_limit(RecordingOptions self) -&gt; int </p>
<h2>Returns </h2>
<p>the data rate (ms) the recorder will read data from the module if set, otherwise returns -1 (no data rate limit). A value of 1000 ms means data is read every second. Data in between is discarded. </p>

</div>
</div>
<a id="a6217bf22e4808de72736154fc99c54fd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6217bf22e4808de72736154fc99c54fd">&sect;&nbsp;</a></span>get_directory_split_size()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RecordingOptions.get_directory_split_size </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_directory_split_size(RecordingOptions self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml" title="The PreferredSplitSize class allows specifying a split size. ">PreferredSplitSize</a> </p>
<p>By default, this function returns a default constructed value (no directory split size) if no custom size is set.</p>
<h2>Returns </h2>
<p>the preferred directory split size</p>
<p>See <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml" title="The PreferredSplitSize class allows specifying a split size. ">PreferredSplitSize</a> </p>

</div>
</div>
<a id="a36c3a4834bf15f78e57918e318086d4a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a36c3a4834bf15f78e57918e318086d4a">&sect;&nbsp;</a></span>get_file_split_size()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RecordingOptions.get_file_split_size </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_file_split_size(RecordingOptions self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml" title="The PreferredSplitSize class allows specifying a split size. ">PreferredSplitSize</a> </p>
<p>By default, this function returns a default constructed value (no file split size) if no custom size is set.</p>
<h2>Returns </h2>
<p>the preferred file split size</p>
<p>See <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml" title="The PreferredSplitSize class allows specifying a split size. ">PreferredSplitSize</a> </p>

</div>
</div>
<a id="a7df54e8220e70427c10478887935a096"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7df54e8220e70427c10478887935a096">&sect;&nbsp;</a></span>get_flush_on_write()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RecordingOptions.get_flush_on_write </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_flush_on_write(RecordingOptions self) -&gt; bool </p>
<h2>Returns </h2>
<p>Whether flushing for every write to file is enabled. </p>

</div>
</div>
<a id="a44620f5f8a32092b863d78e6baac9ad0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44620f5f8a32092b863d78e6baac9ad0">&sect;&nbsp;</a></span>get_session_id()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RecordingOptions.get_session_id </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_session_id(RecordingOptions self) -&gt; std::string </p>
<p>By default, this function returns an universally unique identifier (UUID) if no custom id is set.</p>
<h2>Returns </h2>
<p>the session id</p>
<p>See set_session_id </p>

</div>
</div>
<a id="a115bcb784f8909992cff589ec005722e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a115bcb784f8909992cff589ec005722e">&sect;&nbsp;</a></span>get_user_header()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RecordingOptions.get_user_header </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_user_header(RecordingOptions self) -&gt; ucVector </p>
<h2>Returns </h2>
<p>the custom user header. By default, this parameter is Bytes() (no custom header). </p>

</div>
</div>
<a id="a5f5788127ec56ee7d14ab848299988cb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5f5788127ec56ee7d14ab848299988cb">&sect;&nbsp;</a></span>set_data_rate_limit()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RecordingOptions.set_data_rate_limit </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>limit</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_data_rate_limit(RecordingOptions self, int limit) </p>
<p>Sets the data rate (ms) the recorder will read data from the module.</p>
<p>A value of 1000 ms means data is read every second. Data in between is discarded. By default, this parameter is -1 (no data rate limit).</p>
<h2>Parameters </h2>
<ul>
<li><code>limit</code> : Specifies the data rate limit </li>
</ul>

</div>
</div>
<a id="acf02bee9c4142297511c556d635305d0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acf02bee9c4142297511c556d635305d0">&sect;&nbsp;</a></span>set_directory_split_size()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RecordingOptions.set_directory_split_size </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>size</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_directory_split_size(RecordingOptions self, PreferredSplitSize size) </p>
<p>Sets the preferred directory split size as specified.</p>
<h2>Parameters </h2>
<ul>
<li><code>size</code> : Specifies the preferred split size </li>
</ul>

</div>
</div>
<a id="ad0c2444e6629f7334ae3859ad6946d4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad0c2444e6629f7334ae3859ad6946d4b">&sect;&nbsp;</a></span>set_file_split_size()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RecordingOptions.set_file_split_size </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>size</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_file_split_size(RecordingOptions self, PreferredSplitSize size) </p>
<p>Sets the preferred file split size as specified.</p>
<h2>Parameters </h2>
<ul>
<li><code>size</code> : Specifies the preferred split size </li>
</ul>

</div>
</div>
<a id="aed473a5de7c674635120149aea07e3de"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aed473a5de7c674635120149aea07e3de">&sect;&nbsp;</a></span>set_flush_on_write()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RecordingOptions.set_flush_on_write </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>do_flush</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_flush_on_write(RecordingOptions self, bool do_flush) </p>
<p>Specify whether to flush on every write.</p>
<p>Note that this will impact the performance and should only be used for debugging purposes. The default is false.</p>
<h2>Parameters </h2>
<ul>
<li><code>do_flush</code> : If true, recording files are flushed after every write. </li>
</ul>

</div>
</div>
<a id="a3748f23da0a1e6582e7ac54ec11c400d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3748f23da0a1e6582e7ac54ec11c400d">&sect;&nbsp;</a></span>set_session_id()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RecordingOptions.set_session_id </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>id</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_session_id(RecordingOptions self, std::string const &amp; id) </p>
<p>Sets the session id as specified, overriding the default constructed value which is an universally unique identifier (UUID).</p>
<h2>Parameters </h2>
<ul>
<li><code>id</code> : Specifies the new id</li>
</ul>
<p>See get_session_id </p>

</div>
</div>
<a id="ad1e010d865745ded5018f4dc3b70c6cf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad1e010d865745ded5018f4dc3b70c6cf">&sect;&nbsp;</a></span>set_user_header()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RecordingOptions.set_user_header </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>header</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_user_header(RecordingOptions self, ucVector header) </p>
<p>Sets a custom header applied to the beginning of the recorded file.</p>
<p>By default, this parameter is Bytes() (no custom header).</p>
<h2>Parameters </h2>
<ul>
<li><code>header</code> : Specifies the header </li>
</ul>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/moduleconnectorwrapper/__init__.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
