<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_r"></a>- r -</h3><ul>
<li>read()
: <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg.xhtml#ac8372bbf2e53dbd9daf66b71cd885401">pymoduleconnector.extras.regmap.Reg</a>
</li>
<li>read_message_baseband_ap()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a8ef5b257e1e3f8414a5f0b4b0039c831">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a1f8cea3e7255ec52ee7444b9ae702c8c">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#aeb74a781f50c83a6c79ee9647c6f37a2">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>read_message_baseband_iq()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a72e2c2faab935a92a000a1e82632a887">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a0efb364b393388051d393d7e9853ebc3">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a37a8e78bd1339fe62929623df1870012">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>read_message_data_byte()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a6af6f6e423922d6aa891c24afc2f30f4">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>read_message_data_float()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ad59080c40089562c07b22667a383f256">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>read_message_data_string()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a009858962f1c1a7d68235f155de10af1">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>read_message_noisemap_byte()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a241846da92672ea8553658f0354abef7">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a59cae5165608a77e836138a76322c56a">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a165b9c2579ddf56c6e61071e661e19e1">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>read_message_noisemap_float()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a5441e125cbf16ebba07723bacbd606d4">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ad240dbd66b6513c993ddba2287043d08">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a393fb80799ba2a37d5904d64c874bc39">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>read_message_presence_movinglist()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a4285cf9d408eaca6ee5d67b3e0d33dcc">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>read_message_presence_single()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#aaa2ada346650ef99eb0fe9ea96d690e3">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>read_message_pulsedoppler_byte()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a0aa395ad2d12ad3b63eb8bce94e957cf">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a81acb200ffc094404d15efc3fd952fb1">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a8df10ebe802cdd6e025148c0a4000f7d">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>read_message_pulsedoppler_float()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#af0d271447f6439bb9ff520c74d4d9183">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a475ce19a1fbdc6929a3e35e7af91ae58">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a7f4644b3a1d1832dea72d8b1aedb8372">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>read_message_radar_baseband_float()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aad85ad1bcf681c0181fc7357698a0f07">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>read_message_radar_baseband_q15()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1ed7efc454b67aa9db7557b417b63cfb">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>read_message_radar_rf()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ad4d188597aac757e69b07f1268cf3b7b">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>read_message_radar_rf_normalized()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#abb483b4d0b654083d11edf73ba4b74a0">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>read_message_respiration_detectionlist()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ab1380086b4778bc5ce05bf7017b5f956">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a1f37c89243269814e4f6cbe778435548">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
</li>
<li>read_message_respiration_legacy()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a50066a1b9a6b23735d24cefac8a40c57">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a38ec0a7a881b4669d69ea38fd5bbff56">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
</li>
<li>read_message_respiration_movinglist()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a4293f289aa9e8b951a3e81559e4ce773">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aed360a7cf77b630cb1325606d46c5a7c">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
</li>
<li>read_message_respiration_normalizedmovementlist()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ae672abd46253481b38f049b0b4439070">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ae99b5753066a2cd2a16b99cf2e640838">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
</li>
<li>read_message_respiration_sleep()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#af5edf0b4ef081acc72d29965b429690b">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a96cad1f147acd0dbf4e4f8e1839d613e">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
</li>
<li>read_message_system()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a464fd568aeafaabf14099654f3305e31">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>read_message_vital_signs()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a5c2798d0c7a05a6b62de05f24a329084">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#af7babc3ff1cd64d4fba43358554b4e9f">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
</li>
<li>read_record()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml#a484cf503c0acd9b29742bd4b51bb4aae">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a>
</li>
<li>refresh_shadow()
: <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_block.xhtml#adaa529524b3e92b721c3f5a7c25c67c6">pymoduleconnector.extras.regmap.RegBlock</a>
</li>
<li>reset()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#af4cc47b13396ec86470dc97188521e64">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#afdd6156171a0480f7b859468342fc288">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a2e55660358cf4864723cdb823a5830cb">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a2209379a8d5e0c4d24032b8d1a1ea5fd">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>reset_to_factory_preset()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a877fc62b046d3c161124cca8dbd3fe2a">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a98b89f02120573ecfb151d8474ffe069">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ac26286f535f1d4a628ceec852b8a551f">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
