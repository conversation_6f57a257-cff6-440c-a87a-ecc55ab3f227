var searchData=
[
  ['samplerpresetlsb',['SamplerPresetLsb',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_sampler_preset_lsb.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['samplerpresetmsb',['SamplerPresetMsb',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_sampler_preset_msb.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['sleepdata',['SleepData',['../classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_data.xhtml',1,'pymoduleconnector::moduleconnectorwrapper']]],
  ['sleepstagedata',['SleepStageData',['../classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_stage_data.xhtml',1,'pymoduleconnector::moduleconnectorwrapper']]],
  ['smplmode',['SmplMode',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_smpl_mode.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spi',['SPI',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_s_p_i.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spiconfig',['SpiConfig',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_config.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spiconfigpif',['SpiConfigPif',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_config_pif.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spimasteridle',['SpiMasterIdle',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_idle.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spimastermode',['SpiMasterMode',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_mode.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spimasterradarburstkick',['SpiMasterRadarBurstKick',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_radar_burst_kick.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spimasterradarburstsizelsb',['SpiMasterRadarBurstSizeLsb',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_radar_burst_size_lsb.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spimastersend',['SpiMasterSend',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_send.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spimbclearstatus',['SpiMbClearStatus',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mb_clear_status.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spimbfifostatus',['SpiMbFifoStatus',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mb_fifo_status.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spimemclearstatus',['SpiMemClearStatus',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mem_clear_status.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spimemfifostatus',['SpiMemFifoStatus',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mem_fifo_status.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spiradardata0clearstatus',['SpiRadarData0ClearStatus',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data0_clear_status.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spiradardata0fifostatus',['SpiRadarData0FifoStatus',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data0_fifo_status.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spiradardata1clearstatus',['SpiRadarData1ClearStatus',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data1_clear_status.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spiradardata1fifostatus',['SpiRadarData1FifoStatus',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data1_fifo_status.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]],
  ['spiradardataclearstatus',['SpiRadarDataClearStatus',['../classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data_clear_status.xhtml',1,'pymoduleconnector::extras::x4_regmap_autogen']]]
];
