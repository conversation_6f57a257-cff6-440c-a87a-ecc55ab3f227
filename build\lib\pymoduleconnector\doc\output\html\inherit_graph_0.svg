<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.38.0 (20140413.2041)
 -->
<!-- Title: Graphical Class Hierarchy Pages: 1 -->
<svg width="623pt" height="7403pt"
 viewBox="0.00 0.00 623.00 7403.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 7399)">
<title>Graphical Class Hierarchy</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-7399 619,-7399 619,4 -4,4"/>
<!-- Node8 -->
<g id="node1" class="node"><title>Node8</title>
<g id="a_node1"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_chip_id_dig.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.ChipIdDig">
<polygon fill="white" stroke="black" points="0,-147.5 0,-177.5 145,-177.5 145,-147.5 0,-147.5"/>
<text text-anchor="start" x="8" y="-165.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="72.5" y="-154.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.ChipIdDig</text>
</a>
</g>
</g>
<!-- Node0 -->
<g id="node2" class="node"><title>Node0</title>
<g id="a_node2"><a xlink:href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg.xhtml" target="_top" xlink:title="Generic register class. ">
<polygon fill="white" stroke="black" points="181,-3763 181,-3782 369,-3782 369,-3763 181,-3763"/>
<text text-anchor="middle" x="275" y="-3770" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.regmap.Reg</text>
</a>
</g>
</g>
<!-- Node8&#45;&gt;Node0 -->
<g id="edge1" class="edge"><title>Node8&#45;&gt;Node0</title>
<path fill="none" stroke="midnightblue" d="M74.9787,-187.831C92.6567,-498.675 263.075,-3567.24 273.501,-3762.75"/>
<polygon fill="midnightblue" stroke="midnightblue" points="78.458,-187.366 74.3939,-177.581 71.4693,-187.765 78.458,-187.366"/>
</g>
<!-- Node155 -->
<g id="node147" class="node"><title>Node155</title>
<g id="a_node147"><a xlink:href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_block.xhtml" target="_top" xlink:title="The RegBlock class encapsulates one or more RegMap objects (register maps). ">
<polygon fill="white" stroke="black" points="190,-147.5 190,-177.5 360,-177.5 360,-147.5 190,-147.5"/>
<text text-anchor="start" x="198" y="-165.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.regmap.</text>
<text text-anchor="middle" x="275" y="-154.5" font-family="Helvetica,sans-Serif" font-size="10.00">RegBlock</text>
</a>
</g>
</g>
<!-- Node8&#45;&gt;Node155 -->
<g id="edge147" class="edge"><title>Node8&#45;&gt;Node155</title>
<path fill="none" stroke="midnightblue" d="M155.363,-162.5C166.728,-162.5 178.419,-162.5 189.825,-162.5"/>
<polygon fill="midnightblue" stroke="midnightblue" points="155.036,-159 145.036,-162.5 155.036,-166 155.036,-159"/>
</g>
<!-- Node157 -->
<g id="node149" class="node"><title>Node157</title>
<g id="a_node149"><a xlink:href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_map.xhtml" target="_top" xlink:title="pymoduleconnector.extras.regmap.\lRegMap">
<polygon fill="white" stroke="black" points="190,-98.5 190,-128.5 360,-128.5 360,-98.5 190,-98.5"/>
<text text-anchor="start" x="198" y="-116.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.regmap.</text>
<text text-anchor="middle" x="275" y="-105.5" font-family="Helvetica,sans-Serif" font-size="10.00">RegMap</text>
</a>
</g>
</g>
<!-- Node8&#45;&gt;Node157 -->
<g id="edge149" class="edge"><title>Node8&#45;&gt;Node157</title>
<path fill="none" stroke="midnightblue" d="M145.012,-145.023C167.111,-139.623 191.245,-133.724 212.597,-128.506"/>
<polygon fill="midnightblue" stroke="midnightblue" points="143.902,-141.691 135.019,-147.465 145.564,-148.491 143.902,-141.691"/>
</g>
<!-- Node161 -->
<g id="node153" class="node"><title>Node161</title>
<g id="a_node153"><a xlink:href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_segment.xhtml" target="_top" xlink:title="Register segment class. ">
<polygon fill="white" stroke="black" points="190,-49.5 190,-79.5 360,-79.5 360,-49.5 190,-49.5"/>
<text text-anchor="start" x="198" y="-67.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.regmap.</text>
<text text-anchor="middle" x="275" y="-56.5" font-family="Helvetica,sans-Serif" font-size="10.00">RegSegment</text>
</a>
</g>
</g>
<!-- Node8&#45;&gt;Node161 -->
<g id="edge153" class="edge"><title>Node8&#45;&gt;Node161</title>
<path fill="none" stroke="midnightblue" d="M99.3581,-140.819C120.21,-124.33 150.921,-102.212 181,-88.5 188.435,-85.1109 196.43,-82.1399 204.5,-79.5511"/>
<polygon fill="midnightblue" stroke="midnightblue" points="96.9271,-138.283 91.328,-147.277 101.314,-143.737 96.9271,-138.283"/>
</g>
<!-- Node0&#45;&gt;Node8 -->
<g id="edge9" class="edge"><title>Node0&#45;&gt;Node8</title>
<path fill="none" stroke="midnightblue" d="M272.833,-3752.65C257.099,-3476.87 87.2836,-419.289 74.287,-177.622"/>
<polygon fill="midnightblue" stroke="midnightblue" points="269.348,-3753.01 273.415,-3762.79 276.337,-3752.61 269.348,-3753.01"/>
</g>
<!-- Node1 -->
<g id="node3" class="node"><title>Node1</title>
<g id="a_node3"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_apc_dvdd_testmode.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.ApcDvddTestmode">
<polygon fill="white" stroke="black" points="419.5,-7364.5 419.5,-7394.5 600.5,-7394.5 600.5,-7364.5 419.5,-7364.5"/>
<text text-anchor="start" x="427.5" y="-7382.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-7371.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.ApcDvddTestmode</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node1 -->
<g id="edge2" class="edge"><title>Node0&#45;&gt;Node1</title>
<path fill="none" stroke="midnightblue" d="M276.024,-3792.27C276.443,-4071.14 283.853,-7208.71 405,-7355.5 409.01,-7360.36 413.79,-7364.35 419.064,-7367.62"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.524,-3792.04 276.01,-3782.04 272.524,-3792.05 279.524,-3792.04"/>
</g>
<!-- Node2 -->
<g id="node4" class="node"><title>Node2</title>
<g id="a_node4"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_apc_temp_trim.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.ApcTempTrim">
<polygon fill="white" stroke="black" points="431,-7315.5 431,-7345.5 589,-7345.5 589,-7315.5 431,-7315.5"/>
<text text-anchor="start" x="439" y="-7333.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-7322.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.ApcTempTrim</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node2 -->
<g id="edge3" class="edge"><title>Node0&#45;&gt;Node2</title>
<path fill="none" stroke="midnightblue" d="M276.034,-3792.42C276.596,-4070.17 285.532,-7161.82 405,-7306.5 411.843,-7314.79 420.924,-7320.55 430.877,-7324.51"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.534,-3792.2 276.015,-3782.21 272.534,-3792.21 279.534,-3792.2"/>
</g>
<!-- Node3 -->
<g id="node5" class="node"><title>Node3</title>
<g id="a_node5"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_avdd_rx_ctrl.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.AvddRxCtrl">
<polygon fill="white" stroke="black" points="436,-7266.5 436,-7296.5 584,-7296.5 584,-7266.5 436,-7266.5"/>
<text text-anchor="start" x="444" y="-7284.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-7273.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.AvddRxCtrl</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node3 -->
<g id="edge4" class="edge"><title>Node0&#45;&gt;Node3</title>
<path fill="none" stroke="midnightblue" d="M276.044,-3792.14C276.742,-4066.05 287.149,-7114.85 405,-7257.5 412.966,-7267.14 423.962,-7273.37 435.836,-7277.31"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.544,-3792.06 276.02,-3782.07 272.544,-3792.08 279.544,-3792.06"/>
</g>
<!-- Node4 -->
<g id="node6" class="node"><title>Node4</title>
<g id="a_node6"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_avdd_testmode.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.AvddTestmode">
<polygon fill="white" stroke="black" points="428.5,-7217.5 428.5,-7247.5 591.5,-7247.5 591.5,-7217.5 428.5,-7217.5"/>
<text text-anchor="start" x="436.5" y="-7235.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-7224.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.AvddTestmode</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node4 -->
<g id="edge5" class="edge"><title>Node0&#45;&gt;Node4</title>
<path fill="none" stroke="midnightblue" d="M276.055,-3792.28C276.899,-4064.95 288.826,-7067.96 405,-7208.5 411.257,-7216.07 419.381,-7221.53 428.331,-7225.44"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.555,-3792.22 276.025,-3782.23 272.555,-3792.24 279.555,-3792.22"/>
</g>
<!-- Node5 -->
<g id="node7" class="node"><title>Node5</title>
<g id="a_node7"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_avdd_tx_ctrl.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.AvddTxCtrl">
<polygon fill="white" stroke="black" points="437,-7168.5 437,-7198.5 583,-7198.5 583,-7168.5 437,-7168.5"/>
<text text-anchor="start" x="445" y="-7186.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-7175.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.AvddTxCtrl</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node5 -->
<g id="edge6" class="edge"><title>Node0&#45;&gt;Node5</title>
<path fill="none" stroke="midnightblue" d="M276.067,-3792.4C277.06,-4063.72 290.499,-7021.06 405,-7159.5 413.208,-7169.42 424.624,-7175.73 436.902,-7179.65"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.566,-3792.08 276.03,-3782.09 272.566,-3792.11 279.566,-3792.08"/>
</g>
<!-- Node6 -->
<g id="node8" class="node"><title>Node6</title>
<g id="a_node8"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_boot_from_otp_pif.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.BootFromOtpPif">
<polygon fill="white" stroke="black" points="426.5,-7119.5 426.5,-7149.5 593.5,-7149.5 593.5,-7119.5 426.5,-7119.5"/>
<text text-anchor="start" x="434.5" y="-7137.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-7126.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.BootFromOtpPif</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node6 -->
<g id="edge7" class="edge"><title>Node0&#45;&gt;Node6</title>
<path fill="none" stroke="midnightblue" d="M276.079,-3792.51C277.223,-4062.45 292.172,-6974.16 405,-7110.5 410.789,-7117.5 418.172,-7122.69 426.326,-7126.52"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.578,-3792.23 276.037,-3782.25 272.578,-3792.26 279.578,-3792.23"/>
</g>
<!-- Node7 -->
<g id="node9" class="node"><title>Node7</title>
<g id="a_node9"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_boot_from_otp_spi.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.BootFromOtpSpi">
<polygon fill="white" stroke="black" points="425,-7070.5 425,-7100.5 595,-7100.5 595,-7070.5 425,-7070.5"/>
<text text-anchor="start" x="433" y="-7088.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-7077.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.BootFromOtpSpi</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node7 -->
<g id="edge8" class="edge"><title>Node0&#45;&gt;Node7</title>
<path fill="none" stroke="midnightblue" d="M276.089,-3792.22C277.373,-4058.19 293.787,-6927.19 405,-7061.5 410.436,-7068.07 417.277,-7073.05 424.841,-7076.8"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.589,-3792.09 276.042,-3782.1 272.589,-3792.12 279.589,-3792.09"/>
</g>
<!-- Node9 -->
<g id="node10" class="node"><title>Node9</title>
<g id="a_node10"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_chip_id_sys.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.ChipIdSys">
<polygon fill="white" stroke="black" points="437.5,-7021.5 437.5,-7051.5 582.5,-7051.5 582.5,-7021.5 437.5,-7021.5"/>
<text text-anchor="start" x="445.5" y="-7039.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-7028.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.ChipIdSys</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node9 -->
<g id="edge10" class="edge"><title>Node0&#45;&gt;Node9</title>
<path fill="none" stroke="midnightblue" d="M276.102,-3792.33C277.541,-4056.79 295.458,-6880.29 405,-7012.5 413.335,-7022.56 424.966,-7028.9 437.446,-7032.8"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.601,-3792.23 276.048,-3782.25 272.601,-3792.26 279.601,-3792.23"/>
</g>
<!-- Node10 -->
<g id="node11" class="node"><title>Node10</title>
<g id="a_node11"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_clkout_sel.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.ClkoutSel">
<polygon fill="white" stroke="black" points="437.5,-6972.5 437.5,-7002.5 582.5,-7002.5 582.5,-6972.5 437.5,-6972.5"/>
<text text-anchor="start" x="445.5" y="-6990.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6979.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.ClkoutSel</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node10 -->
<g id="edge11" class="edge"><title>Node0&#45;&gt;Node10</title>
<path fill="none" stroke="midnightblue" d="M276.115,-3792.42C277.712,-4055.25 297.126,-6833.38 405,-6963.5 413.338,-6973.56 424.971,-6979.89 437.451,-6983.8"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.613,-3792.08 276.053,-3782.1 272.613,-3792.12 279.613,-3792.08"/>
</g>
<!-- Node11 -->
<g id="node12" class="node"><title>Node11</title>
<g id="a_node12"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_ctrl1.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.CommonPllCtrl1">
<polygon fill="white" stroke="black" points="425.5,-6923.5 425.5,-6953.5 594.5,-6953.5 594.5,-6923.5 425.5,-6923.5"/>
<text text-anchor="start" x="433.5" y="-6941.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6930.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.CommonPllCtrl1</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node11 -->
<g id="edge12" class="edge"><title>Node0&#45;&gt;Node11</title>
<path fill="none" stroke="midnightblue" d="M276.128,-3792.5C277.885,-4053.67 298.793,-6786.47 405,-6914.5 410.561,-6921.2 417.585,-6926.25 425.348,-6930.03"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.626,-3792.21 276.061,-3782.23 272.627,-3792.25 279.626,-3792.21"/>
</g>
<!-- Node12 -->
<g id="node13" class="node"><title>Node12</title>
<g id="a_node13"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_ctrl2.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.CommonPllCtrl2">
<polygon fill="white" stroke="black" points="425.5,-6874.5 425.5,-6904.5 594.5,-6904.5 594.5,-6874.5 425.5,-6874.5"/>
<text text-anchor="start" x="433.5" y="-6892.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6881.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.CommonPllCtrl2</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node12 -->
<g id="edge13" class="edge"><title>Node0&#45;&gt;Node12</title>
<path fill="none" stroke="midnightblue" d="M276.139,-3792.19C278.04,-4049.29 300.405,-6739.5 405,-6865.5 410.563,-6872.2 417.588,-6877.25 425.352,-6881.03"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.638,-3792.05 276.066,-3782.08 272.639,-3792.11 279.638,-3792.05"/>
</g>
<!-- Node13 -->
<g id="node14" class="node"><title>Node13</title>
<g id="a_node14"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_ctrl3.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.CommonPllCtrl3">
<polygon fill="white" stroke="black" points="425.5,-6825.5 425.5,-6855.5 594.5,-6855.5 594.5,-6825.5 425.5,-6825.5"/>
<text text-anchor="start" x="433.5" y="-6843.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6832.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.CommonPllCtrl3</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node13 -->
<g id="edge14" class="edge"><title>Node0&#45;&gt;Node13</title>
<path fill="none" stroke="midnightblue" d="M276.153,-3792.27C278.218,-4047.58 302.07,-6692.59 405,-6816.5 410.565,-6823.2 417.592,-6828.25 425.357,-6832.03"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.653,-3792.18 276.074,-3782.2 272.653,-3792.23 279.653,-3792.18"/>
</g>
<!-- Node14 -->
<g id="node15" class="node"><title>Node14</title>
<g id="a_node15"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_ctrl4.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.CommonPllCtrl4">
<polygon fill="white" stroke="black" points="425.5,-6776.5 425.5,-6806.5 594.5,-6806.5 594.5,-6776.5 425.5,-6776.5"/>
<text text-anchor="start" x="433.5" y="-6794.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6783.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.CommonPllCtrl4</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node14 -->
<g id="edge15" class="edge"><title>Node0&#45;&gt;Node14</title>
<path fill="none" stroke="midnightblue" d="M276.168,-3792.32C278.399,-4045.73 303.732,-6645.68 405,-6767.5 410.567,-6774.2 417.596,-6779.24 425.361,-6783.02"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.666,-3792.02 276.079,-3782.05 272.666,-3792.08 279.666,-3792.02"/>
</g>
<!-- Node15 -->
<g id="node16" class="node"><title>Node15</title>
<g id="a_node16"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_frac0.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.CommonPllFrac0">
<polygon fill="white" stroke="black" points="423.5,-6727.5 423.5,-6757.5 596.5,-6757.5 596.5,-6727.5 423.5,-6727.5"/>
<text text-anchor="start" x="431.5" y="-6745.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6734.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.CommonPllFrac0</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node15 -->
<g id="edge16" class="edge"><title>Node0&#45;&gt;Node15</title>
<path fill="none" stroke="midnightblue" d="M276.183,-3792.38C278.583,-4043.85 305.393,-6598.76 405,-6718.5 410.096,-6724.63 416.414,-6729.37 423.4,-6733.03"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.681,-3792.13 276.087,-3782.16 272.681,-3792.2 279.681,-3792.13"/>
</g>
<!-- Node16 -->
<g id="node17" class="node"><title>Node16</title>
<g id="a_node17"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_frac1.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.CommonPllFrac1">
<polygon fill="white" stroke="black" points="423.5,-6678.5 423.5,-6708.5 596.5,-6708.5 596.5,-6678.5 423.5,-6678.5"/>
<text text-anchor="start" x="431.5" y="-6696.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6685.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.CommonPllFrac1</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node16 -->
<g id="edge17" class="edge"><title>Node0&#45;&gt;Node16</title>
<path fill="none" stroke="midnightblue" d="M276.195,-3792.05C278.742,-4039.34 307.003,-6551.79 405,-6669.5 410.098,-6675.62 416.418,-6680.37 423.404,-6684.02"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.694,-3791.97 276.093,-3782 272.695,-3792.04 279.694,-3791.97"/>
</g>
<!-- Node17 -->
<g id="node18" class="node"><title>Node17</title>
<g id="a_node18"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_frac2.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.CommonPllFrac2">
<polygon fill="white" stroke="black" points="423.5,-6629.5 423.5,-6659.5 596.5,-6659.5 596.5,-6629.5 423.5,-6629.5"/>
<text text-anchor="start" x="431.5" y="-6647.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6636.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.CommonPllFrac2</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node17 -->
<g id="edge18" class="edge"><title>Node0&#45;&gt;Node17</title>
<path fill="none" stroke="midnightblue" d="M276.215,-3792.45C278.96,-4039.78 308.71,-6504.93 405,-6620.5 410.1,-6626.62 416.421,-6631.36 423.409,-6635.02"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.711,-3792.07 276.102,-3782.11 272.711,-3792.15 279.711,-3792.07"/>
</g>
<!-- Node18 -->
<g id="node19" class="node"><title>Node18</title>
<g id="a_node19"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_cpu_reset.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.CpuReset">
<polygon fill="white" stroke="black" points="437.5,-6580.5 437.5,-6610.5 582.5,-6610.5 582.5,-6580.5 437.5,-6580.5"/>
<text text-anchor="start" x="445.5" y="-6598.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6587.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.CpuReset</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node18 -->
<g id="edge19" class="edge"><title>Node0&#45;&gt;Node18</title>
<path fill="none" stroke="midnightblue" d="M276.231,-3792.48C279.153,-4037.63 310.366,-6458.01 405,-6571.5 413.366,-6581.53 425.012,-6587.86 437.496,-6591.76"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.728,-3792.17 276.11,-3782.21 272.729,-3792.25 279.728,-3792.17"/>
</g>
<!-- Node19 -->
<g id="node20" class="node"><title>Node19</title>
<g id="a_node20"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_cpu_spi_master_clk_ctrl.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.CpuSpiMasterClkCtrl">
<polygon fill="white" stroke="black" points="415,-6531.5 415,-6561.5 605,-6561.5 605,-6531.5 415,-6531.5"/>
<text text-anchor="start" x="423" y="-6549.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6538.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.CpuSpiMasterClkCtrl</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node19 -->
<g id="edge20" class="edge"><title>Node0&#45;&gt;Node19</title>
<path fill="none" stroke="midnightblue" d="M276.244,-3792.13C279.316,-4032.99 311.973,-6411.03 405,-6522.5 407.849,-6525.91 411.079,-6528.9 414.591,-6531.51"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.742,-3791.99 276.117,-3782.04 272.743,-3792.08 279.742,-3791.99"/>
</g>
<!-- Node20 -->
<g id="node21" class="node"><title>Node20</title>
<g id="a_node21"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dac_anatestreq.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.DacAnatestreq">
<polygon fill="white" stroke="black" points="429,-6482.5 429,-6512.5 591,-6512.5 591,-6482.5 429,-6482.5"/>
<text text-anchor="start" x="437" y="-6500.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6489.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.DacAnatestreq</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node20 -->
<g id="edge21" class="edge"><title>Node0&#45;&gt;Node20</title>
<path fill="none" stroke="midnightblue" d="M276.261,-3792.14C279.514,-4030.72 313.627,-6364.1 405,-6473.5 411.414,-6481.18 419.75,-6486.69 428.911,-6490.6"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.761,-3792.08 276.126,-3782.13 272.761,-3792.17 279.761,-3792.08"/>
</g>
<!-- Node21 -->
<g id="node22" class="node"><title>Node21</title>
<g id="a_node22"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dac_trim.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.DacTrim">
<polygon fill="white" stroke="black" points="437.5,-6433.5 437.5,-6463.5 582.5,-6463.5 582.5,-6433.5 437.5,-6433.5"/>
<text text-anchor="start" x="445.5" y="-6451.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6440.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.DacTrim</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node21 -->
<g id="edge22" class="edge"><title>Node0&#45;&gt;Node21</title>
<path fill="none" stroke="midnightblue" d="M276.284,-3792.5C279.75,-4030.63 315.323,-6317.23 405,-6424.5 413.319,-6434.45 424.865,-6440.76 437.251,-6444.66"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.78,-3792.16 276.136,-3782.21 272.78,-3792.26 279.78,-3792.16"/>
</g>
<!-- Node22 -->
<g id="node23" class="node"><title>Node22</title>
<g id="a_node23"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_debug.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.Debug">
<polygon fill="white" stroke="black" points="437.5,-6384.5 437.5,-6414.5 582.5,-6414.5 582.5,-6384.5 437.5,-6384.5"/>
<text text-anchor="start" x="445.5" y="-6402.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6391.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.Debug</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node22 -->
<g id="edge23" class="edge"><title>Node0&#45;&gt;Node22</title>
<path fill="none" stroke="midnightblue" d="M276.297,-3792.13C279.917,-4025.86 316.928,-6270.25 405,-6375.5 413.324,-6385.45 424.872,-6391.75 437.258,-6395.66"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.795,-3791.98 276.142,-3782.03 272.796,-3792.08 279.795,-3791.98"/>
</g>
<!-- Node23 -->
<g id="node24" class="node"><title>Node23</title>
<g id="a_node24"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_debug_xif.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.DebugXif">
<polygon fill="white" stroke="black" points="437.5,-6335.5 437.5,-6365.5 582.5,-6365.5 582.5,-6335.5 437.5,-6335.5"/>
<text text-anchor="start" x="445.5" y="-6353.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6342.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.DebugXif</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node23 -->
<g id="edge24" class="edge"><title>Node0&#45;&gt;Node23</title>
<path fill="none" stroke="midnightblue" d="M276.316,-3792.11C280.123,-4023.33 318.577,-6223.32 405,-6326.5 413.329,-6336.44 424.879,-6342.74 437.265,-6346.65"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.816,-3792.04 276.153,-3782.1 272.816,-3792.16 279.816,-3792.04"/>
</g>
<!-- Node24 -->
<g id="node25" class="node"><title>Node24</title>
<g id="a_node25"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dvdd_rx_ctrl.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.DvddRxCtrl">
<polygon fill="white" stroke="black" points="435.5,-6286.5 435.5,-6316.5 584.5,-6316.5 584.5,-6286.5 435.5,-6286.5"/>
<text text-anchor="start" x="443.5" y="-6304.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6293.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.DvddRxCtrl</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node24 -->
<g id="edge25" class="edge"><title>Node0&#45;&gt;Node24</title>
<path fill="none" stroke="midnightblue" d="M276.341,-3792.42C280.372,-4022.85 320.266,-6176.43 405,-6277.5 412.917,-6286.94 423.738,-6293.1 435.421,-6297.04"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.837,-3792.11 276.164,-3782.17 272.838,-3792.23 279.837,-3792.11"/>
</g>
<!-- Node25 -->
<g id="node26" class="node"><title>Node25</title>
<g id="a_node26"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dvdd_testmode.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.DvddTestmode">
<polygon fill="white" stroke="black" points="428,-6237.5 428,-6267.5 592,-6267.5 592,-6237.5 428,-6237.5"/>
<text text-anchor="start" x="436" y="-6255.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6244.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.DvddTestmode</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node25 -->
<g id="edge26" class="edge"><title>Node0&#45;&gt;Node25</title>
<path fill="none" stroke="midnightblue" d="M276.368,-3792.73C280.628,-4022.23 321.953,-6129.55 405,-6228.5 411.194,-6235.88 419.163,-6241.25 427.93,-6245.13"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.863,-3792.41 276.179,-3782.48 272.864,-3792.54 279.863,-3792.41"/>
</g>
<!-- Node26 -->
<g id="node27" class="node"><title>Node26</title>
<g id="a_node27"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dvdd_tx_ctrl.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.DvddTxCtrl">
<polygon fill="white" stroke="black" points="436.5,-6188.5 436.5,-6218.5 583.5,-6218.5 583.5,-6188.5 436.5,-6188.5"/>
<text text-anchor="start" x="444.5" y="-6206.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6195.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.DvddTxCtrl</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node26 -->
<g id="edge27" class="edge"><title>Node0&#45;&gt;Node26</title>
<path fill="none" stroke="midnightblue" d="M276.382,-3792.32C280.802,-4017.26 323.553,-6082.56 405,-6179.5 413.165,-6189.22 424.407,-6195.45 436.494,-6199.38"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.881,-3792.21 276.187,-3782.28 272.882,-3792.35 279.881,-3792.21"/>
</g>
<!-- Node27 -->
<g id="node28" class="node"><title>Node27</title>
<g id="a_node28"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_fetch_radar_data_pif.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.FetchRadarDataPif">
<polygon fill="white" stroke="black" points="419.5,-6139.5 419.5,-6169.5 600.5,-6169.5 600.5,-6139.5 419.5,-6139.5"/>
<text text-anchor="start" x="427.5" y="-6157.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6146.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.FetchRadarDataPif</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node27 -->
<g id="edge28" class="edge"><title>Node0&#45;&gt;Node27</title>
<path fill="none" stroke="midnightblue" d="M276.403,-3792.25C281.021,-4014.29 325.193,-6035.62 405,-6130.5 409.055,-6135.32 413.868,-6139.29 419.165,-6142.53"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.899,-3792.01 276.194,-3782.08 272.901,-3792.15 279.899,-3792.01"/>
</g>
<!-- Node28 -->
<g id="node29" class="node"><title>Node28</title>
<g id="a_node29"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_fetch_radar_data_spi.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.FetchRadarDataSpi">
<polygon fill="white" stroke="black" points="418.5,-6090.5 418.5,-6120.5 601.5,-6120.5 601.5,-6090.5 418.5,-6090.5"/>
<text text-anchor="start" x="426.5" y="-6108.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6097.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.FetchRadarDataSpi</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node28 -->
<g id="edge29" class="edge"><title>Node0&#45;&gt;Node28</title>
<path fill="none" stroke="midnightblue" d="M276.433,-3792.51C281.289,-4013.28 326.872,-5988.73 405,-6081.5 408.82,-6086.04 413.309,-6089.81 418.242,-6092.95"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.929,-3792.28 276.211,-3782.36 272.93,-3792.44 279.929,-3792.28"/>
</g>
<!-- Node29 -->
<g id="node30" class="node"><title>Node29</title>
<g id="a_node30"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_firmware_version.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.FirmwareVersion">
<polygon fill="white" stroke="black" points="425,-6041.5 425,-6071.5 595,-6071.5 595,-6041.5 425,-6041.5"/>
<text text-anchor="start" x="433" y="-6059.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-6048.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.FirmwareVersion</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node29 -->
<g id="edge30" class="edge"><title>Node0&#45;&gt;Node29</title>
<path fill="none" stroke="midnightblue" d="M276.455,-3792.41C281.515,-4010.1 328.508,-5941.78 405,-6032.5 410.495,-6039.02 417.372,-6043.97 424.959,-6047.7"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.948,-3792.07 276.219,-3782.15 272.95,-3792.23 279.948,-3792.07"/>
</g>
<!-- Node30 -->
<g id="node31" class="node"><title>Node30</title>
<g id="a_node31"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_firmware_version_spi.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.FirmwareVersionSpi">
<polygon fill="white" stroke="black" points="417.5,-5992.5 417.5,-6022.5 602.5,-6022.5 602.5,-5992.5 417.5,-5992.5"/>
<text text-anchor="start" x="425.5" y="-6010.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5999.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.FirmwareVersionSpi</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node30 -->
<g id="edge31" class="edge"><title>Node0&#45;&gt;Node30</title>
<path fill="none" stroke="midnightblue" d="M276.486,-3792.63C281.793,-4008.79 330.182,-5894.88 405,-5983.5 408.586,-5987.75 412.76,-5991.33 417.334,-5994.34"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.98,-3792.33 276.237,-3782.42 272.982,-3792.5 279.98,-3792.33"/>
</g>
<!-- Node31 -->
<g id="node32" class="node"><title>Node31</title>
<g id="a_node32"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_force_one.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.ForceOne">
<polygon fill="white" stroke="black" points="437.5,-5943.5 437.5,-5973.5 582.5,-5973.5 582.5,-5943.5 437.5,-5943.5"/>
<text text-anchor="start" x="445.5" y="-5961.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5950.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.ForceOne</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node31 -->
<g id="edge32" class="edge"><title>Node0&#45;&gt;Node31</title>
<path fill="none" stroke="midnightblue" d="M276.51,-3792.5C282.026,-4005.39 331.813,-5847.93 405,-5934.5 413.374,-5944.41 424.944,-5950.69 437.337,-5954.59"/>
<polygon fill="midnightblue" stroke="midnightblue" points="280.001,-3792.1 276.245,-3782.2 273.004,-3792.28 280.001,-3792.1"/>
</g>
<!-- Node32 -->
<g id="node33" class="node"><title>Node32</title>
<g id="a_node33"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_force_zero.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.ForceZero">
<polygon fill="white" stroke="black" points="437.5,-5894.5 437.5,-5924.5 582.5,-5924.5 582.5,-5894.5 437.5,-5894.5"/>
<text text-anchor="start" x="445.5" y="-5912.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5901.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.ForceZero</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node32 -->
<g id="edge33" class="edge"><title>Node0&#45;&gt;Node32</title>
<path fill="none" stroke="midnightblue" d="M276.544,-3792.69C282.315,-4003.78 333.481,-5801.02 405,-5885.5 413.381,-5895.4 424.954,-5901.68 437.348,-5905.58"/>
<polygon fill="midnightblue" stroke="midnightblue" points="280.036,-3792.34 276.265,-3782.44 273.038,-3792.53 280.036,-3792.34"/>
</g>
<!-- Node33 -->
<g id="node34" class="node"><title>Node33</title>
<g id="a_node34"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_from_cpu_read_data.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.FromCpuReadData">
<polygon fill="white" stroke="black" points="419,-5845.5 419,-5875.5 601,-5875.5 601,-5845.5 419,-5845.5"/>
<text text-anchor="start" x="427" y="-5863.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5852.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.FromCpuReadData</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node33 -->
<g id="edge34" class="edge"><title>Node0&#45;&gt;Node33</title>
<path fill="none" stroke="midnightblue" d="M275.828,-3792.27C274.25,-3993.82 265.359,-5671.79 405,-5836.5 408.954,-5841.16 413.617,-5845.02 418.741,-5848.21"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.328,-3792.25 275.91,-3782.22 272.328,-3792.2 279.328,-3792.25"/>
</g>
<!-- Node34 -->
<g id="node35" class="node"><title>Node34</title>
<g id="a_node35"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_from_cpu_write_data.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.FromCpuWriteData">
<polygon fill="white" stroke="black" points="419,-5796.5 419,-5826.5 601,-5826.5 601,-5796.5 419,-5796.5"/>
<text text-anchor="start" x="427" y="-5814.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5803.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.FromCpuWriteData</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node34 -->
<g id="edge35" class="edge"><title>Node0&#45;&gt;Node34</title>
<path fill="none" stroke="midnightblue" d="M275.857,-3792.71C274.615,-3993.54 268.746,-5627.03 405,-5787.5 408.958,-5792.16 413.623,-5796.02 418.749,-5799.2"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.358,-3792.46 275.924,-3782.43 272.359,-3792.41 279.358,-3792.46"/>
</g>
<!-- Node35 -->
<g id="node36" class="node"><title>Node35</title>
<g id="a_node36"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_from_mem_read_data.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.FromMemReadData">
<polygon fill="white" stroke="black" points="417.5,-5747.5 417.5,-5777.5 602.5,-5777.5 602.5,-5747.5 417.5,-5747.5"/>
<text text-anchor="start" x="425.5" y="-5765.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5754.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.FromMemReadData</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node35 -->
<g id="edge36" class="edge"><title>Node0&#45;&gt;Node35</title>
<path fill="none" stroke="midnightblue" d="M275.893,-3792.22C275.007,-3988.18 271.921,-5582.02 405,-5738.5 408.601,-5742.73 412.787,-5746.31 417.37,-5749.31"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.393,-3792.21 275.942,-3782.19 272.393,-3792.18 279.393,-3792.21"/>
</g>
<!-- Node36 -->
<g id="node37" class="node"><title>Node36</title>
<g id="a_node37"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_gpio_in.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.GpioIn">
<polygon fill="white" stroke="black" points="437.5,-5698.5 437.5,-5728.5 582.5,-5728.5 582.5,-5698.5 437.5,-5698.5"/>
<text text-anchor="start" x="445.5" y="-5716.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5705.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.GpioIn</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node36 -->
<g id="edge37" class="edge"><title>Node0&#45;&gt;Node36</title>
<path fill="none" stroke="midnightblue" d="M275.927,-3792.61C275.397,-3987.53 275.294,-5537.24 405,-5689.5 413.412,-5699.37 424.999,-5705.64 437.396,-5709.54"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.427,-3792.39 275.959,-3782.38 272.427,-3792.37 279.427,-3792.39"/>
</g>
<!-- Node37 -->
<g id="node38" class="node"><title>Node37</title>
<g id="a_node38"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_gpio_oe.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.GpioOe">
<polygon fill="white" stroke="black" points="437.5,-5649.5 437.5,-5679.5 582.5,-5679.5 582.5,-5649.5 437.5,-5649.5"/>
<text text-anchor="start" x="445.5" y="-5667.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5656.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.GpioOe</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node37 -->
<g id="edge38" class="edge"><title>Node0&#45;&gt;Node37</title>
<path fill="none" stroke="midnightblue" d="M275.964,-3792.38C275.8,-3983.55 278.527,-5492.3 405,-5640.5 413.42,-5650.37 425.012,-5656.63 437.41,-5660.53"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.465,-3792.13 275.977,-3782.13 272.465,-3792.12 279.465,-3792.13"/>
</g>
<!-- Node38 -->
<g id="node39" class="node"><title>Node38</title>
<g id="a_node39"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_gpio_out.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.GpioOut">
<polygon fill="white" stroke="black" points="437.5,-5600.5 437.5,-5630.5 582.5,-5630.5 582.5,-5600.5 437.5,-5600.5"/>
<text text-anchor="start" x="445.5" y="-5618.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5607.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.GpioOut</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node38 -->
<g id="edge39" class="edge"><title>Node0&#45;&gt;Node38</title>
<path fill="none" stroke="midnightblue" d="M276.003,-3792.43C276.214,-3981.03 281.821,-5447.43 405,-5591.5 413.429,-5601.36 425.025,-5607.62 437.425,-5611.52"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.503,-3792.29 275.996,-3782.29 272.503,-3792.29 279.503,-3792.29"/>
</g>
<!-- Node39 -->
<g id="node40" class="node"><title>Node39</title>
<g id="a_node40"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl1.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.IoCtrl1">
<polygon fill="white" stroke="black" points="437.5,-5551.5 437.5,-5581.5 582.5,-5581.5 582.5,-5551.5 437.5,-5551.5"/>
<text text-anchor="start" x="445.5" y="-5569.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5558.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.IoCtrl1</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node39 -->
<g id="edge40" class="edge"><title>Node0&#45;&gt;Node39</title>
<path fill="none" stroke="midnightblue" d="M276.044,-3792.17C276.631,-3976.84 285.045,-5402.48 405,-5542.5 413.439,-5552.35 425.039,-5558.61 437.44,-5562.5"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.543,-3792.02 276.016,-3782.03 272.543,-3792.04 279.543,-3792.02"/>
</g>
<!-- Node40 -->
<g id="node41" class="node"><title>Node40</title>
<g id="a_node41"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl2.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.IoCtrl2">
<polygon fill="white" stroke="black" points="437.5,-5502.5 437.5,-5532.5 582.5,-5532.5 582.5,-5502.5 437.5,-5502.5"/>
<text text-anchor="start" x="445.5" y="-5520.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5509.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.IoCtrl2</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node40 -->
<g id="edge41" class="edge"><title>Node0&#45;&gt;Node40</title>
<path fill="none" stroke="midnightblue" d="M276.087,-3792.18C277.065,-3974.03 288.327,-5357.6 405,-5493.5 413.449,-5503.34 425.054,-5509.6 437.456,-5513.49"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.587,-3792.15 276.037,-3782.17 272.587,-3792.18 279.587,-3792.15"/>
</g>
<!-- Node41 -->
<g id="node42" class="node"><title>Node41</title>
<g id="a_node42"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl3.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.IoCtrl3">
<polygon fill="white" stroke="black" points="437.5,-5453.5 437.5,-5483.5 582.5,-5483.5 582.5,-5453.5 437.5,-5453.5"/>
<text text-anchor="start" x="445.5" y="-5471.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5460.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.IoCtrl3</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node41 -->
<g id="edge42" class="edge"><title>Node0&#45;&gt;Node41</title>
<path fill="none" stroke="midnightblue" d="M276.134,-3792.44C277.524,-3972.4 291.658,-5312.77 405,-5444.5 413.46,-5454.33 425.069,-5460.58 437.473,-5464.47"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.633,-3792.26 276.06,-3782.29 272.633,-3792.31 279.633,-3792.26"/>
</g>
<!-- Node42 -->
<g id="node43" class="node"><title>Node42</title>
<g id="a_node43"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl4.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.IoCtrl4">
<polygon fill="white" stroke="black" points="437.5,-5404.5 437.5,-5434.5 582.5,-5434.5 582.5,-5404.5 437.5,-5404.5"/>
<text text-anchor="start" x="445.5" y="-5422.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5411.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.IoCtrl4</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node42 -->
<g id="edge43" class="edge"><title>Node0&#45;&gt;Node42</title>
<path fill="none" stroke="midnightblue" d="M276.18,-3792.12C277.966,-3967.85 294.867,-5267.8 405,-5395.5 413.471,-5405.32 425.086,-5411.57 437.491,-5415.46"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.679,-3791.97 276.081,-3782 272.679,-3792.04 279.679,-3791.97"/>
</g>
<!-- Node43 -->
<g id="node44" class="node"><title>Node43</title>
<g id="a_node44"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl5.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.IoCtrl5">
<polygon fill="white" stroke="black" points="437.5,-5355.5 437.5,-5385.5 582.5,-5385.5 582.5,-5355.5 437.5,-5355.5"/>
<text text-anchor="start" x="445.5" y="-5373.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5362.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.IoCtrl5</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node43 -->
<g id="edge44" class="edge"><title>Node0&#45;&gt;Node43</title>
<path fill="none" stroke="midnightblue" d="M276.233,-3792.31C278.453,-3965.81 298.182,-5222.95 405,-5346.5 413.423,-5356.24 424.937,-5362.46 437.244,-5366.36"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.731,-3792.06 276.107,-3782.1 272.731,-3792.14 279.731,-3792.06"/>
</g>
<!-- Node44 -->
<g id="node45" class="node"><title>Node44</title>
<g id="a_node45"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl6.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.IoCtrl6">
<polygon fill="white" stroke="black" points="437.5,-5306.5 437.5,-5336.5 582.5,-5336.5 582.5,-5306.5 437.5,-5306.5"/>
<text text-anchor="start" x="445.5" y="-5324.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5313.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.IoCtrl6</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node44 -->
<g id="edge45" class="edge"><title>Node0&#45;&gt;Node44</title>
<path fill="none" stroke="midnightblue" d="M276.287,-3792.22C278.935,-3962.3 301.434,-5178.03 405,-5297.5 413.435,-5307.23 424.956,-5313.45 437.265,-5317.34"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.786,-3792.12 276.135,-3782.18 272.787,-3792.23 279.786,-3792.12"/>
</g>
<!-- Node45 -->
<g id="node46" class="node"><title>Node45</title>
<g id="a_node46"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_iref_trim.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.IrefTrim">
<polygon fill="white" stroke="black" points="437.5,-5257.5 437.5,-5287.5 582.5,-5287.5 582.5,-5257.5 437.5,-5257.5"/>
<text text-anchor="start" x="445.5" y="-5275.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5264.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.IrefTrim</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node45 -->
<g id="edge46" class="edge"><title>Node0&#45;&gt;Node45</title>
<path fill="none" stroke="midnightblue" d="M276.352,-3792.6C279.479,-3960.98 304.78,-5133.21 405,-5248.5 413.449,-5258.22 424.976,-5264.43 437.286,-5268.32"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.845,-3792.18 276.164,-3782.24 272.846,-3792.3 279.845,-3792.18"/>
</g>
<!-- Node46 -->
<g id="node47" class="node"><title>Node46</title>
<g id="a_node47"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_ldo_status1.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.LdoStatus1">
<polygon fill="white" stroke="black" points="437,-5208.5 437,-5238.5 583,-5238.5 583,-5208.5 437,-5208.5"/>
<text text-anchor="start" x="445" y="-5226.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5215.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.LdoStatus1</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node46 -->
<g id="edge47" class="edge"><title>Node0&#45;&gt;Node46</title>
<path fill="none" stroke="midnightblue" d="M276.412,-3792.44C279.988,-3957.07 308.016,-5088.27 405,-5199.5 413.342,-5209.07 424.665,-5215.23 436.778,-5219.13"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.908,-3792.21 276.196,-3782.28 272.909,-3792.36 279.908,-3792.21"/>
</g>
<!-- Node47 -->
<g id="node48" class="node"><title>Node47</title>
<g id="a_node48"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_ldo_status2.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.LdoStatus2">
<polygon fill="white" stroke="black" points="437,-5159.5 437,-5189.5 583,-5189.5 583,-5159.5 437,-5159.5"/>
<text text-anchor="start" x="445" y="-5177.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5166.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.LdoStatus2</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node47 -->
<g id="edge48" class="edge"><title>Node0&#45;&gt;Node47</title>
<path fill="none" stroke="midnightblue" d="M276.486,-3792.74C280.57,-3955.17 311.338,-5043.42 405,-5150.5 413.357,-5160.05 424.688,-5166.21 436.803,-5170.11"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.975,-3792.22 276.229,-3782.31 272.977,-3792.39 279.975,-3792.22"/>
</g>
<!-- Node48 -->
<g id="node49" class="node"><title>Node48</title>
<g id="a_node49"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_lna_anatestreq.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.LnaAnatestreq">
<polygon fill="white" stroke="black" points="430,-5110.5 430,-5140.5 590,-5140.5 590,-5110.5 430,-5110.5"/>
<text text-anchor="start" x="438" y="-5128.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5117.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.LnaAnatestreq</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node48 -->
<g id="edge49" class="edge"><title>Node0&#45;&gt;Node48</title>
<path fill="none" stroke="midnightblue" d="M276.553,-3792.51C281.107,-3950.86 314.555,-4998.46 405,-5101.5 411.723,-5109.16 420.358,-5114.64 429.772,-5118.51"/>
<polygon fill="midnightblue" stroke="midnightblue" points="280.046,-3792.21 276.265,-3782.32 273.049,-3792.41 280.046,-3792.21"/>
</g>
<!-- Node49 -->
<g id="node50" class="node"><title>Node49</title>
<g id="a_node50"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_lock_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.LockStatus">
<polygon fill="white" stroke="black" points="437,-5061.5 437,-5091.5 583,-5091.5 583,-5061.5 437,-5061.5"/>
<text text-anchor="start" x="445" y="-5079.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5068.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.LockStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node49 -->
<g id="edge50" class="edge"><title>Node0&#45;&gt;Node49</title>
<path fill="none" stroke="midnightblue" d="M276.638,-3792.71C281.728,-3948.39 317.853,-4953.58 405,-5052.5 413.391,-5062.02 424.737,-5068.17 436.857,-5072.06"/>
<polygon fill="midnightblue" stroke="midnightblue" points="280.123,-3792.19 276.303,-3782.3 273.126,-3792.41 280.123,-3792.19"/>
</g>
<!-- Node50 -->
<g id="node51" class="node"><title>Node50</title>
<g id="a_node51"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mclk_trx_backend_clk_ctrl.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.MclkTrxBackendClkCtrl">
<polygon fill="white" stroke="black" points="409.5,-5012.5 409.5,-5042.5 610.5,-5042.5 610.5,-5012.5 409.5,-5012.5"/>
<text text-anchor="start" x="417.5" y="-5030.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-5019.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.MclkTrxBackendClkCtrl</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node50 -->
<g id="edge51" class="edge"><title>Node0&#45;&gt;Node50</title>
<path fill="none" stroke="midnightblue" d="M276.711,-3792.4C282.292,-3943.68 321.053,-4908.59 405,-5003.5 407.946,-5006.83 411.255,-5009.75 414.83,-5012.3"/>
<polygon fill="midnightblue" stroke="midnightblue" points="280.204,-3792.13 276.342,-3782.27 273.208,-3792.39 280.204,-3792.13"/>
</g>
<!-- Node51 -->
<g id="node52" class="node"><title>Node51</title>
<g id="a_node52"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mcu_bist_ctrl.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.McuBistCtrl">
<polygon fill="white" stroke="black" points="435.5,-4963.5 435.5,-4993.5 584.5,-4993.5 584.5,-4963.5 435.5,-4963.5"/>
<text text-anchor="start" x="443.5" y="-4981.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-4970.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.McuBistCtrl</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node51 -->
<g id="edge52" class="edge"><title>Node0&#45;&gt;Node51</title>
<path fill="none" stroke="midnightblue" d="M276.806,-3792.51C282.949,-3940.65 324.326,-4863.69 405,-4954.5 413.061,-4963.57 423.805,-4969.58 435.332,-4973.48"/>
<polygon fill="midnightblue" stroke="midnightblue" points="280.291,-3792.06 276.384,-3782.21 273.297,-3792.35 280.291,-3792.06"/>
</g>
<!-- Node52 -->
<g id="node53" class="node"><title>Node52</title>
<g id="a_node53"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mcu_bist_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.McuBistStatus">
<polygon fill="white" stroke="black" points="429.5,-4914.5 429.5,-4944.5 590.5,-4944.5 590.5,-4914.5 429.5,-4914.5"/>
<text text-anchor="start" x="437.5" y="-4932.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-4921.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.McuBistStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node52 -->
<g id="edge53" class="edge"><title>Node0&#45;&gt;Node52</title>
<path fill="none" stroke="midnightblue" d="M276.887,-3792.13C283.54,-3935.53 327.508,-4818.68 405,-4905.5 411.663,-4912.97 420.143,-4918.35 429.376,-4922.2"/>
<polygon fill="midnightblue" stroke="midnightblue" points="280.383,-3791.96 276.429,-3782.13 273.391,-3792.28 280.383,-3791.96"/>
</g>
<!-- Node53 -->
<g id="node54" class="node"><title>Node53</title>
<g id="a_node54"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mem_first_addr_lsb.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.MemFirstAddrLsb">
<polygon fill="white" stroke="black" points="423,-4865.5 423,-4895.5 597,-4895.5 597,-4865.5 423,-4865.5"/>
<text text-anchor="start" x="431" y="-4883.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-4872.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.MemFirstAddrLsb</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node53 -->
<g id="edge54" class="edge"><title>Node0&#45;&gt;Node53</title>
<path fill="none" stroke="midnightblue" d="M276.993,-3792.13C284.235,-3931.94 330.756,-4773.74 405,-4856.5 410.073,-4862.16 416.189,-4866.61 422.884,-4870.12"/>
<polygon fill="midnightblue" stroke="midnightblue" points="280.483,-3791.84 276.475,-3782.03 273.492,-3792.2 280.483,-3791.84"/>
</g>
<!-- Node54 -->
<g id="node55" class="node"><title>Node54</title>
<g id="a_node55"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mem_first_addr_msb.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.MemFirstAddrMsb">
<polygon fill="white" stroke="black" points="421.5,-4816.5 421.5,-4846.5 598.5,-4846.5 598.5,-4816.5 421.5,-4816.5"/>
<text text-anchor="start" x="429.5" y="-4834.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-4823.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.MemFirstAddrMsb</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node54 -->
<g id="edge55" class="edge"><title>Node0&#45;&gt;Node54</title>
<path fill="none" stroke="midnightblue" d="M277.129,-3792.49C285.052,-3929.69 334.064,-4728.87 405,-4807.5 409.717,-4812.73 415.325,-4816.93 421.455,-4820.3"/>
<polygon fill="midnightblue" stroke="midnightblue" points="280.607,-3792 276.542,-3782.22 273.618,-3792.4 280.607,-3792"/>
</g>
<!-- Node55 -->
<g id="node56" class="node"><title>Node55</title>
<g id="a_node56"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mem_mode.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.MemMode">
<polygon fill="white" stroke="black" points="437.5,-4767.5 437.5,-4797.5 582.5,-4797.5 582.5,-4767.5 437.5,-4767.5"/>
<text text-anchor="start" x="445.5" y="-4785.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-4774.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.MemMode</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node55 -->
<g id="edge56" class="edge"><title>Node0&#45;&gt;Node55</title>
<path fill="none" stroke="midnightblue" d="M275.651,-3792.16C273.832,-3917.98 270.118,-4609.9 405,-4758.5 413.593,-4767.97 425.11,-4774.07 437.349,-4777.95"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.152,-3792.12 275.812,-3782.06 272.153,-3792 279.152,-3792.12"/>
</g>
<!-- Node56 -->
<g id="node57" class="node"><title>Node56</title>
<g id="a_node57"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_misc_ctrl.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.MiscCtrl">
<polygon fill="white" stroke="black" points="437.5,-4718.5 437.5,-4748.5 582.5,-4748.5 582.5,-4718.5 437.5,-4718.5"/>
<text text-anchor="start" x="445.5" y="-4736.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-4725.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.MiscCtrl</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node56 -->
<g id="edge57" class="edge"><title>Node0&#45;&gt;Node56</title>
<path fill="none" stroke="midnightblue" d="M275.798,-3792.31C274.964,-3914.87 276.629,-4569.02 405,-4709.5 413.625,-4718.94 425.156,-4725.03 437.4,-4728.9"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.299,-3792.18 275.883,-3782.15 272.3,-3792.12 279.299,-3792.18"/>
</g>
<!-- Node57 -->
<g id="node58" class="node"><title>Node57</title>
<g id="a_node58"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_osc_ctrl.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.OscCtrl">
<polygon fill="white" stroke="black" points="437.5,-4669.5 437.5,-4699.5 582.5,-4699.5 582.5,-4669.5 437.5,-4669.5"/>
<text text-anchor="start" x="445.5" y="-4687.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-4676.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.OscCtrl</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node57 -->
<g id="edge58" class="edge"><title>Node0&#45;&gt;Node57</title>
<path fill="none" stroke="midnightblue" d="M275.963,-3792.38C276.158,-3911.32 283.097,-4528.1 405,-4660.5 413.66,-4669.91 425.207,-4675.99 437.456,-4679.85"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.464,-3792.19 275.962,-3782.19 272.464,-3792.19 279.464,-3792.19"/>
</g>
<!-- Node58 -->
<g id="node59" class="node"><title>Node58</title>
<g id="a_node59"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_otp_ctrl.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.OtpCtrl">
<polygon fill="white" stroke="black" points="437.5,-4620.5 437.5,-4650.5 582.5,-4650.5 582.5,-4620.5 437.5,-4620.5"/>
<text text-anchor="start" x="445.5" y="-4638.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-4627.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.OtpCtrl</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node58 -->
<g id="edge59" class="edge"><title>Node0&#45;&gt;Node58</title>
<path fill="none" stroke="midnightblue" d="M276.148,-3792.35C277.415,-3907.33 289.521,-4487.11 405,-4611.5 413.636,-4620.8 425.097,-4626.84 437.252,-4630.71"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.646,-3792.15 276.052,-3782.19 272.646,-3792.22 279.646,-3792.15"/>
</g>
<!-- Node59 -->
<g id="node60" class="node"><title>Node59</title>
<g id="a_node60"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_mb_clear_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.PifMbClearStatus">
<polygon fill="white" stroke="black" points="423,-4571.5 423,-4601.5 597,-4601.5 597,-4571.5 423,-4571.5"/>
<text text-anchor="start" x="431" y="-4589.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-4578.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.PifMbClearStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node59 -->
<g id="edge60" class="edge"><title>Node0&#45;&gt;Node59</title>
<path fill="none" stroke="midnightblue" d="M276.352,-3792.22C278.731,-3902.91 295.901,-4446.08 405,-4562.5 410.068,-4567.91 416.092,-4572.21 422.648,-4575.63"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.849,-3792.06 276.151,-3782.13 272.851,-3792.2 279.849,-3792.06"/>
</g>
<!-- Node60 -->
<g id="node61" class="node"><title>Node60</title>
<g id="a_node61"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_mb_fifo_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.PifMbFifoStatus">
<polygon fill="white" stroke="black" points="426.5,-4522.5 426.5,-4552.5 593.5,-4552.5 593.5,-4522.5 426.5,-4522.5"/>
<text text-anchor="start" x="434.5" y="-4540.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-4529.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.PifMbFifoStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node60 -->
<g id="edge61" class="edge"><title>Node0&#45;&gt;Node60</title>
<path fill="none" stroke="midnightblue" d="M276.587,-3792.33C280.154,-3899.13 302.342,-4405.1 405,-4513.5 411.052,-4519.89 418.439,-4524.74 426.456,-4528.39"/>
<polygon fill="midnightblue" stroke="midnightblue" points="280.084,-3792.17 276.268,-3782.28 273.087,-3792.39 280.084,-3792.17"/>
</g>
<!-- Node61 -->
<g id="node62" class="node"><title>Node61</title>
<g id="a_node62"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_mem_clear_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.PifMemClearStatus">
<polygon fill="white" stroke="black" points="419,-4473.5 419,-4503.5 601,-4503.5 601,-4473.5 419,-4473.5"/>
<text text-anchor="start" x="427" y="-4491.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-4480.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.PifMemClearStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node61 -->
<g id="edge62" class="edge"><title>Node0&#45;&gt;Node61</title>
<path fill="none" stroke="midnightblue" d="M276.85,-3792.29C281.654,-3894.73 308.72,-4364.04 405,-4464.5 409.103,-4468.78 413.805,-4472.37 418.892,-4475.36"/>
<polygon fill="midnightblue" stroke="midnightblue" points="280.338,-3791.95 276.391,-3782.12 273.345,-3792.27 280.338,-3791.95"/>
</g>
<!-- Node62 -->
<g id="node63" class="node"><title>Node62</title>
<g id="a_node63"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_mem_fifo_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.PifMemFifoStatus">
<polygon fill="white" stroke="black" points="422,-4424.5 422,-4454.5 598,-4454.5 598,-4424.5 422,-4424.5"/>
<text text-anchor="start" x="430" y="-4442.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-4431.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.PifMemFifoStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node62 -->
<g id="edge63" class="edge"><title>Node0&#45;&gt;Node62</title>
<path fill="none" stroke="midnightblue" d="M277.162,-3792.42C283.3,-3890.72 315.134,-4323.01 405,-4415.5 409.907,-4420.55 415.648,-4424.63 421.869,-4427.92"/>
<polygon fill="midnightblue" stroke="midnightblue" points="280.651,-3792.13 276.552,-3782.36 273.664,-3792.55 280.651,-3792.13"/>
</g>
<!-- Node63 -->
<g id="node64" class="node"><title>Node63</title>
<g id="a_node64"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data0_clear_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.PifRadarData0Clear\lStatus">
<polygon fill="white" stroke="black" points="417.5,-4365 417.5,-4406 602.5,-4406 602.5,-4365 417.5,-4365"/>
<text text-anchor="start" x="425.5" y="-4394" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="425.5" y="-4383" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.PifRadarData0Clear</text>
<text text-anchor="middle" x="510" y="-4372" font-family="Helvetica,sans-Serif" font-size="10.00">Status</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node63 -->
<g id="edge64" class="edge"><title>Node0&#45;&gt;Node63</title>
<path fill="none" stroke="midnightblue" d="M277.718,-3792.48C286.06,-3884.96 325.22,-4270.52 405,-4355.5 408.73,-4359.47 412.95,-4362.93 417.5,-4365.94"/>
<polygon fill="midnightblue" stroke="midnightblue" points="281.183,-3791.92 276.816,-3782.27 274.21,-3792.54 281.183,-3791.92"/>
</g>
<!-- Node64 -->
<g id="node65" class="node"><title>Node64</title>
<g id="a_node65"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data0_fifo_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.PifRadarData0Fifo\lStatus">
<polygon fill="white" stroke="black" points="421,-4305 421,-4346 599,-4346 599,-4305 421,-4305"/>
<text text-anchor="start" x="429" y="-4334" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="429" y="-4323" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.PifRadarData0Fifo</text>
<text text-anchor="middle" x="510" y="-4312" font-family="Helvetica,sans-Serif" font-size="10.00">Status</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node64 -->
<g id="edge65" class="edge"><title>Node0&#45;&gt;Node64</title>
<path fill="none" stroke="midnightblue" d="M278.211,-3792.04C288.294,-3877.84 332.568,-4220.19 405,-4295.5 409.689,-4300.38 415.117,-4304.47 420.981,-4307.92"/>
<polygon fill="midnightblue" stroke="midnightblue" points="281.685,-3791.62 277.06,-3782.09 274.732,-3792.42 281.685,-3791.62"/>
</g>
<!-- Node65 -->
<g id="node66" class="node"><title>Node65</title>
<g id="a_node66"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data1_clear_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.PifRadarData1Clear\lStatus">
<polygon fill="white" stroke="black" points="417.5,-4245 417.5,-4286 602.5,-4286 602.5,-4245 417.5,-4245"/>
<text text-anchor="start" x="425.5" y="-4274" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="425.5" y="-4263" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.PifRadarData1Clear</text>
<text text-anchor="middle" x="510" y="-4252" font-family="Helvetica,sans-Serif" font-size="10.00">Status</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node65 -->
<g id="edge66" class="edge"><title>Node0&#45;&gt;Node65</title>
<path fill="none" stroke="midnightblue" d="M278.914,-3792.29C291.06,-3872.2 340.062,-4169.97 405,-4235.5 408.704,-4239.24 412.841,-4242.52 417.273,-4245.39"/>
<polygon fill="midnightblue" stroke="midnightblue" points="282.336,-3791.5 277.392,-3782.13 275.413,-3792.54 282.336,-3791.5"/>
</g>
<!-- Node66 -->
<g id="node67" class="node"><title>Node66</title>
<g id="a_node67"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data1_fifo_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.PifRadarData1Fifo\lStatus">
<polygon fill="white" stroke="black" points="421,-4185 421,-4226 599,-4226 599,-4185 421,-4185"/>
<text text-anchor="start" x="429" y="-4214" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="429" y="-4203" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.PifRadarData1Fifo</text>
<text text-anchor="middle" x="510" y="-4192" font-family="Helvetica,sans-Serif" font-size="10.00">Status</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node66 -->
<g id="edge67" class="edge"><title>Node0&#45;&gt;Node66</title>
<path fill="none" stroke="midnightblue" d="M276.158,-3792.08C277.774,-3856.75 291.915,-4065.64 405,-4175.5 409.718,-4180.08 415.089,-4183.97 420.848,-4187.28"/>
<polygon fill="midnightblue" stroke="midnightblue" points="279.657,-3791.98 275.983,-3782.04 272.658,-3792.1 279.657,-3791.98"/>
</g>
<!-- Node67 -->
<g id="node68" class="node"><title>Node67</title>
<g id="a_node68"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data_clear_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.PifRadarDataClear\lStatus">
<polygon fill="white" stroke="black" points="420.5,-4125 420.5,-4166 599.5,-4166 599.5,-4125 420.5,-4125"/>
<text text-anchor="start" x="428.5" y="-4154" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="428.5" y="-4143" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.PifRadarDataClear</text>
<text text-anchor="middle" x="510" y="-4132" font-family="Helvetica,sans-Serif" font-size="10.00">Status</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node67 -->
<g id="edge68" class="edge"><title>Node0&#45;&gt;Node67</title>
<path fill="none" stroke="midnightblue" d="M277.365,-3792.27C282.503,-3850.94 305.824,-4025.58 405,-4116.5 409.56,-4120.68 414.672,-4124.26 420.119,-4127.34"/>
<polygon fill="midnightblue" stroke="midnightblue" points="280.842,-3791.84 276.563,-3782.15 273.864,-3792.39 280.842,-3791.84"/>
</g>
<!-- Node68 -->
<g id="node69" class="node"><title>Node68</title>
<g id="a_node69"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_preamp_trim.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.PreampTrim">
<polygon fill="white" stroke="black" points="435,-4076.5 435,-4106.5 585,-4106.5 585,-4076.5 435,-4076.5"/>
<text text-anchor="start" x="443" y="-4094.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-4083.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.PreampTrim</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node68 -->
<g id="edge69" class="edge"><title>Node0&#45;&gt;Node68</title>
<path fill="none" stroke="midnightblue" d="M278.549,-3792.44C286.488,-3845.92 315.833,-3993.5 405,-4067.5 413.627,-4074.66 423.985,-4079.77 434.774,-4083.41"/>
<polygon fill="midnightblue" stroke="midnightblue" points="281.977,-3791.67 277.132,-3782.25 275.043,-3792.64 281.977,-3791.67"/>
</g>
<!-- Node69 -->
<g id="node70" class="node"><title>Node69</title>
<g id="a_node70"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_bist_ctrl.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RadarBistCtrl">
<polygon fill="white" stroke="black" points="432,-4027.5 432,-4057.5 588,-4057.5 588,-4027.5 432,-4027.5"/>
<text text-anchor="start" x="440" y="-4045.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-4034.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RadarBistCtrl</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node69 -->
<g id="edge70" class="edge"><title>Node0&#45;&gt;Node69</title>
<path fill="none" stroke="midnightblue" d="M280.298,-3791.93C291.723,-3838.8 327.423,-3958.63 405,-4018.5 412.995,-4024.67 422.299,-4029.3 431.993,-4032.77"/>
<polygon fill="midnightblue" stroke="midnightblue" points="283.692,-3791.07 278.009,-3782.13 276.876,-3792.67 283.692,-3791.07"/>
</g>
<!-- Node70 -->
<g id="node71" class="node"><title>Node70</title>
<g id="a_node71"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_bist_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RadarBistStatus">
<polygon fill="white" stroke="black" points="425.5,-3978.5 425.5,-4008.5 594.5,-4008.5 594.5,-3978.5 425.5,-3978.5"/>
<text text-anchor="start" x="433.5" y="-3996.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3985.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RadarBistStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node70 -->
<g id="edge71" class="edge"><title>Node0&#45;&gt;Node70</title>
<path fill="none" stroke="midnightblue" d="M282.884,-3791.76C298.182,-3831.66 338.957,-3923.45 405,-3969.5 411.232,-3973.85 418.135,-3977.41 425.335,-3980.34"/>
<polygon fill="midnightblue" stroke="midnightblue" points="286.084,-3790.33 279.319,-3782.17 279.523,-3792.76 286.084,-3790.33"/>
</g>
<!-- Node71 -->
<g id="node72" class="node"><title>Node71</title>
<g id="a_node72"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_data_pif.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RadarDataPif">
<polygon fill="white" stroke="black" points="432,-3929.5 432,-3959.5 588,-3959.5 588,-3929.5 432,-3929.5"/>
<text text-anchor="start" x="440" y="-3947.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3936.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RadarDataPif</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node71 -->
<g id="edge72" class="edge"><title>Node0&#45;&gt;Node71</title>
<path fill="none" stroke="midnightblue" d="M286.684,-3791.05C306.004,-3822.83 349.904,-3887.45 405,-3920.5 413.263,-3925.46 422.46,-3929.39 431.871,-3932.51"/>
<polygon fill="midnightblue" stroke="midnightblue" points="289.61,-3789.12 281.493,-3782.31 283.592,-3792.7 289.61,-3789.12"/>
</g>
<!-- Node72 -->
<g id="node73" class="node"><title>Node72</title>
<g id="a_node73"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_data_pif_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RadarDataPifStatus">
<polygon fill="white" stroke="black" points="417.5,-3880.5 417.5,-3910.5 602.5,-3910.5 602.5,-3880.5 417.5,-3880.5"/>
<text text-anchor="start" x="425.5" y="-3898.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3887.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RadarDataPifStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node72 -->
<g id="edge73" class="edge"><title>Node0&#45;&gt;Node72</title>
<path fill="none" stroke="midnightblue" d="M292.666,-3789.17C315.829,-3811.36 360.004,-3850.28 405,-3871.5 412.29,-3874.94 420.106,-3877.9 428.058,-3880.45"/>
<polygon fill="midnightblue" stroke="midnightblue" points="294.987,-3786.54 285.382,-3782.07 290.102,-3791.56 294.987,-3786.54"/>
</g>
<!-- Node73 -->
<g id="node74" class="node"><title>Node73</title>
<g id="a_node74"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_data_spi.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RadarDataSpi">
<polygon fill="white" stroke="black" points="431,-3831.5 431,-3861.5 589,-3861.5 589,-3831.5 431,-3831.5"/>
<text text-anchor="start" x="439" y="-3849.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3838.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RadarDataSpi</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node73 -->
<g id="edge74" class="edge"><title>Node0&#45;&gt;Node73</title>
<path fill="none" stroke="midnightblue" d="M307.32,-3786.09C333.173,-3796.96 371.002,-3812.05 405,-3822.5 415.45,-3825.71 426.607,-3828.72 437.591,-3831.44"/>
<polygon fill="midnightblue" stroke="midnightblue" points="308.604,-3782.84 298.031,-3782.15 305.872,-3789.28 308.604,-3782.84"/>
</g>
<!-- Node74 -->
<g id="node75" class="node"><title>Node74</title>
<g id="a_node75"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_data_spi_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RadarDataSpiStatus">
<polygon fill="white" stroke="black" points="416.5,-3782.5 416.5,-3812.5 603.5,-3812.5 603.5,-3782.5 416.5,-3782.5"/>
<text text-anchor="start" x="424.5" y="-3800.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3789.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RadarDataSpiStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node74 -->
<g id="edge75" class="edge"><title>Node0&#45;&gt;Node74</title>
<path fill="none" stroke="midnightblue" d="M374.706,-3783.09C388.475,-3784.57 402.582,-3786.08 416.236,-3787.55"/>
<polygon fill="midnightblue" stroke="midnightblue" points="375.068,-3779.61 364.752,-3782.02 374.321,-3786.57 375.068,-3779.61"/>
</g>
<!-- Node75 -->
<g id="node76" class="node"><title>Node75</title>
<g id="a_node76"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_readout_idle.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RadarReadoutIdle">
<polygon fill="white" stroke="black" points="422,-3733.5 422,-3763.5 598,-3763.5 598,-3733.5 422,-3733.5"/>
<text text-anchor="start" x="430" y="-3751.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3740.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RadarReadoutIdle</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node75 -->
<g id="edge76" class="edge"><title>Node0&#45;&gt;Node75</title>
<path fill="none" stroke="midnightblue" d="M378.427,-3761.95C392.962,-3760.45 407.794,-3758.92 421.999,-3757.46"/>
<polygon fill="midnightblue" stroke="midnightblue" points="378.011,-3758.47 368.422,-3762.98 378.728,-3765.44 378.011,-3758.47"/>
</g>
<!-- Node76 -->
<g id="node77" class="node"><title>Node76</title>
<g id="a_node77"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_ram_select.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RamSelect">
<polygon fill="white" stroke="black" points="437,-3684.5 437,-3714.5 583,-3714.5 583,-3684.5 437,-3684.5"/>
<text text-anchor="start" x="445" y="-3702.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3691.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RamSelect</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node76 -->
<g id="edge77" class="edge"><title>Node0&#45;&gt;Node76</title>
<path fill="none" stroke="midnightblue" d="M308.337,-3759.23C334.207,-3748.92 371.559,-3734.7 405,-3724.5 416.634,-3720.95 429.123,-3717.58 441.268,-3714.53"/>
<polygon fill="midnightblue" stroke="midnightblue" points="306.99,-3756 299.016,-3762.98 309.6,-3762.5 306.99,-3756"/>
</g>
<!-- Node77 -->
<g id="node78" class="node"><title>Node77</title>
<g id="a_node78"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_counter_lsb.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxCounterLsb">
<polygon fill="white" stroke="black" points="430,-3635.5 430,-3665.5 590,-3665.5 590,-3635.5 430,-3635.5"/>
<text text-anchor="start" x="438" y="-3653.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3642.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxCounterLsb</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node77 -->
<g id="edge78" class="edge"><title>Node0&#45;&gt;Node77</title>
<path fill="none" stroke="midnightblue" d="M293.117,-3755.91C316.482,-3734.2 360.558,-3696.44 405,-3675.5 413.147,-3671.66 421.94,-3668.38 430.842,-3665.58"/>
<polygon fill="midnightblue" stroke="midnightblue" points="290.616,-3753.46 285.75,-3762.87 295.423,-3758.55 290.616,-3753.46"/>
</g>
<!-- Node78 -->
<g id="node79" class="node"><title>Node78</title>
<g id="a_node79"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_counter_num_bytes.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxCounterNumBytes">
<polygon fill="white" stroke="black" points="414.5,-3586.5 414.5,-3616.5 605.5,-3616.5 605.5,-3586.5 414.5,-3586.5"/>
<text text-anchor="start" x="422.5" y="-3604.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3593.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxCounterNumBytes</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node78 -->
<g id="edge79" class="edge"><title>Node0&#45;&gt;Node78</title>
<path fill="none" stroke="midnightblue" d="M286.781,-3754.26C306.247,-3722.99 350.364,-3659.36 405,-3626.5 411.445,-3622.62 418.453,-3619.35 425.686,-3616.58"/>
<polygon fill="midnightblue" stroke="midnightblue" points="283.758,-3752.49 281.546,-3762.85 289.737,-3756.13 283.758,-3752.49"/>
</g>
<!-- Node79 -->
<g id="node80" class="node"><title>Node79</title>
<g id="a_node80"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_downconversion_coeff_i1.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxDownconversion\lCoeffI1">
<polygon fill="white" stroke="black" points="419,-3527 419,-3568 601,-3568 601,-3527 419,-3527"/>
<text text-anchor="start" x="427" y="-3556" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="427" y="-3545" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxDownconversion</text>
<text text-anchor="middle" x="510" y="-3534" font-family="Helvetica,sans-Serif" font-size="10.00">CoeffI1</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node79 -->
<g id="edge80" class="edge"><title>Node0&#45;&gt;Node79</title>
<path fill="none" stroke="midnightblue" d="M283.364,-3753.16C299.316,-3713.91 340.874,-3624.61 405,-3577.5 409.846,-3573.94 415.106,-3570.8 420.6,-3568.03"/>
<polygon fill="midnightblue" stroke="midnightblue" points="280.044,-3752.04 279.624,-3762.63 286.554,-3754.61 280.044,-3752.04"/>
</g>
<!-- Node80 -->
<g id="node81" class="node"><title>Node80</title>
<g id="a_node81"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_downconversion_coeff_i2.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxDownconversion\lCoeffI2">
<polygon fill="white" stroke="black" points="419,-3467 419,-3508 601,-3508 601,-3467 419,-3467"/>
<text text-anchor="start" x="427" y="-3496" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="427" y="-3485" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxDownconversion</text>
<text text-anchor="middle" x="510" y="-3474" font-family="Helvetica,sans-Serif" font-size="10.00">CoeffI2</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node80 -->
<g id="edge81" class="edge"><title>Node0&#45;&gt;Node80</title>
<path fill="none" stroke="midnightblue" d="M280.211,-3752.97C291.53,-3705.25 327.173,-3581.95 405,-3517.5 409.197,-3514.02 413.778,-3510.95 418.602,-3508.23"/>
<polygon fill="midnightblue" stroke="midnightblue" points="276.753,-3752.39 277.948,-3762.92 283.579,-3753.95 276.753,-3752.39"/>
</g>
<!-- Node81 -->
<g id="node82" class="node"><title>Node81</title>
<g id="a_node82"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_downconversion_coeff_q1.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxDownconversion\lCoeffQ1">
<polygon fill="white" stroke="black" points="419,-3407 419,-3448 601,-3448 601,-3407 419,-3407"/>
<text text-anchor="start" x="427" y="-3436" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="427" y="-3425" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxDownconversion</text>
<text text-anchor="middle" x="510" y="-3414" font-family="Helvetica,sans-Serif" font-size="10.00">CoeffQ1</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node81 -->
<g id="edge82" class="edge"><title>Node0&#45;&gt;Node81</title>
<path fill="none" stroke="midnightblue" d="M278.189,-3752.62C285.36,-3697.23 313.235,-3539.87 405,-3457.5 409.195,-3453.73 413.836,-3450.44 418.759,-3447.56"/>
<polygon fill="midnightblue" stroke="midnightblue" points="274.68,-3752.48 276.957,-3762.82 281.63,-3753.32 274.68,-3752.48"/>
</g>
<!-- Node82 -->
<g id="node83" class="node"><title>Node82</title>
<g id="a_node83"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_downconversion_coeff_q2.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxDownconversion\lCoeffQ2">
<polygon fill="white" stroke="black" points="419,-3347 419,-3388 601,-3388 601,-3347 419,-3347"/>
<text text-anchor="start" x="427" y="-3376" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="427" y="-3365" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxDownconversion</text>
<text text-anchor="middle" x="510" y="-3354" font-family="Helvetica,sans-Serif" font-size="10.00">CoeffQ2</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node82 -->
<g id="edge83" class="edge"><title>Node0&#45;&gt;Node82</title>
<path fill="none" stroke="midnightblue" d="M276.71,-3752.68C280.031,-3690.66 298.827,-3498.44 405,-3397.5 409.222,-3393.49 413.946,-3390 418.99,-3386.98"/>
<polygon fill="midnightblue" stroke="midnightblue" points="273.2,-3752.84 276.242,-3762.99 280.192,-3753.16 273.2,-3752.84"/>
</g>
<!-- Node83 -->
<g id="node84" class="node"><title>Node83</title>
<g id="a_node84"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_fe_anatestreq.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxFeAnatestreq">
<polygon fill="white" stroke="black" points="426,-3298.5 426,-3328.5 594,-3328.5 594,-3298.5 426,-3298.5"/>
<text text-anchor="start" x="434" y="-3316.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3305.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxFeAnatestreq</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node83 -->
<g id="edge84" class="edge"><title>Node0&#45;&gt;Node83</title>
<path fill="none" stroke="midnightblue" d="M275.385,-3752.5C274.427,-3683.61 281.044,-3454.06 405,-3337.5 411.075,-3331.79 418.227,-3327.34 425.891,-3323.89"/>
<polygon fill="midnightblue" stroke="midnightblue" points="271.892,-3752.87 275.607,-3762.79 278.891,-3752.72 271.892,-3752.87"/>
</g>
<!-- Node84 -->
<g id="node85" class="node"><title>Node84</title>
<g id="a_node85"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_mframes.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxMframes">
<polygon fill="white" stroke="black" points="436,-3249.5 436,-3279.5 584,-3279.5 584,-3249.5 436,-3249.5"/>
<text text-anchor="start" x="444" y="-3267.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3256.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxMframes</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node84 -->
<g id="edge85" class="edge"><title>Node0&#45;&gt;Node84</title>
<path fill="none" stroke="midnightblue" d="M278.564,-3752.83C289.646,-3670.75 336.011,-3356.78 405,-3289.5 413.575,-3281.14 424.431,-3275.4 435.874,-3271.5"/>
<polygon fill="midnightblue" stroke="midnightblue" points="275.084,-3752.44 277.235,-3762.82 282.023,-3753.37 275.084,-3752.44"/>
</g>
<!-- Node85 -->
<g id="node86" class="node"><title>Node85</title>
<g id="a_node86"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_mframes_coarse.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxMframesCoarse">
<polygon fill="white" stroke="black" points="420.5,-3200.5 420.5,-3230.5 599.5,-3230.5 599.5,-3200.5 420.5,-3200.5"/>
<text text-anchor="start" x="428.5" y="-3218.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3207.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxMframesCoarse</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node85 -->
<g id="edge86" class="edge"><title>Node0&#45;&gt;Node85</title>
<path fill="none" stroke="midnightblue" d="M278.079,-3752.54C287.601,-3664.94 329.839,-3315.51 405,-3240.5 409.469,-3236.04 414.585,-3232.32 420.098,-3229.23"/>
<polygon fill="midnightblue" stroke="midnightblue" points="274.575,-3752.4 276.994,-3762.71 281.535,-3753.14 274.575,-3752.4"/>
</g>
<!-- Node86 -->
<g id="node87" class="node"><title>Node86</title>
<g id="a_node87"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_ctrl1.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxPllCtrl1">
<polygon fill="white" stroke="black" points="437.5,-3151.5 437.5,-3181.5 582.5,-3181.5 582.5,-3151.5 437.5,-3151.5"/>
<text text-anchor="start" x="445.5" y="-3169.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3158.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxPllCtrl1</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node86 -->
<g id="edge87" class="edge"><title>Node0&#45;&gt;Node86</title>
<path fill="none" stroke="midnightblue" d="M277.645,-3752.51C285.658,-3659.98 323.568,-3274.36 405,-3191.5 413.851,-3182.49 425.338,-3176.51 437.435,-3172.57"/>
<polygon fill="midnightblue" stroke="midnightblue" points="274.136,-3752.46 276.78,-3762.72 281.111,-3753.05 274.136,-3752.46"/>
</g>
<!-- Node87 -->
<g id="node88" class="node"><title>Node87</title>
<g id="a_node88"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_ctrl2.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxPllCtrl2">
<polygon fill="white" stroke="black" points="437.5,-3102.5 437.5,-3132.5 582.5,-3132.5 582.5,-3102.5 437.5,-3102.5"/>
<text text-anchor="start" x="445.5" y="-3120.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3109.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxPllCtrl2</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node87 -->
<g id="edge88" class="edge"><title>Node0&#45;&gt;Node87</title>
<path fill="none" stroke="midnightblue" d="M277.261,-3752.7C283.824,-3655.88 317.203,-3233.33 405,-3142.5 413.776,-3133.42 425.228,-3127.4 437.315,-3123.46"/>
<polygon fill="midnightblue" stroke="midnightblue" points="273.757,-3752.64 276.591,-3762.85 280.742,-3753.1 273.757,-3752.64"/>
</g>
<!-- Node88 -->
<g id="node89" class="node"><title>Node88</title>
<g id="a_node89"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_skewcalin.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxPllSkewcalin">
<polygon fill="white" stroke="black" points="427,-3053.5 427,-3083.5 593,-3083.5 593,-3053.5 427,-3053.5"/>
<text text-anchor="start" x="435" y="-3071.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3060.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxPllSkewcalin</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node88 -->
<g id="edge89" class="edge"><title>Node0&#45;&gt;Node88</title>
<path fill="none" stroke="midnightblue" d="M276.957,-3752.46C282.223,-3650.69 310.946,-3192.2 405,-3093.5 411.149,-3087.05 418.649,-3082.14 426.776,-3078.41"/>
<polygon fill="midnightblue" stroke="midnightblue" points="273.454,-3752.43 276.45,-3762.59 280.446,-3752.78 273.454,-3752.43"/>
</g>
<!-- Node89 -->
<g id="node90" class="node"><title>Node89</title>
<g id="a_node90"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_skew_ctrl.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxPllSkewCtrl">
<polygon fill="white" stroke="black" points="429.5,-3004.5 429.5,-3034.5 590.5,-3034.5 590.5,-3004.5 429.5,-3004.5"/>
<text text-anchor="start" x="437.5" y="-3022.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-3011.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxPllSkewCtrl</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node89 -->
<g id="edge90" class="edge"><title>Node0&#45;&gt;Node89</title>
<path fill="none" stroke="midnightblue" d="M276.67,-3752.71C280.645,-3647.16 304.517,-3151.26 405,-3044.5 411.746,-3037.33 420.156,-3032.06 429.252,-3028.21"/>
<polygon fill="midnightblue" stroke="midnightblue" points="273.165,-3752.78 276.304,-3762.9 280.161,-3753.03 273.165,-3752.78"/>
</g>
<!-- Node90 -->
<g id="node91" class="node"><title>Node90</title>
<g id="a_node91"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxPllStatus">
<polygon fill="white" stroke="black" points="435.5,-2955.5 435.5,-2985.5 584.5,-2985.5 584.5,-2955.5 435.5,-2955.5"/>
<text text-anchor="start" x="443.5" y="-2973.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2962.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxPllStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node90 -->
<g id="edge91" class="edge"><title>Node0&#45;&gt;Node90</title>
<path fill="none" stroke="midnightblue" d="M276.428,-3752.77C279.208,-3643.19 298.132,-3110.28 405,-2995.5 413.225,-2986.67 423.978,-2980.71 435.445,-2976.73"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.929,-3752.69 276.19,-3762.78 279.927,-3752.86 272.929,-3752.69"/>
</g>
<!-- Node91 -->
<g id="node92" class="node"><title>Node91</title>
<g id="a_node92"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_ram_line_first_msb.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxRamLineFirstMsb">
<polygon fill="white" stroke="black" points="416.5,-2906.5 416.5,-2936.5 603.5,-2936.5 603.5,-2906.5 416.5,-2906.5"/>
<text text-anchor="start" x="424.5" y="-2924.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2913.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxRamLineFirstMsb</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node91 -->
<g id="edge92" class="edge"><title>Node0&#45;&gt;Node91</title>
<path fill="none" stroke="midnightblue" d="M276.213,-3752.97C277.848,-3639.84 291.683,-3069.39 405,-2946.5 408.525,-2942.68 412.523,-2939.39 416.848,-2936.57"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.713,-3752.93 276.084,-3762.97 279.713,-3753.02 272.713,-3752.93"/>
</g>
<!-- Node92 -->
<g id="node93" class="node"><title>Node92</title>
<g id="a_node93"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_ram_line_last_msb.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxRamLineLastMsb">
<polygon fill="white" stroke="black" points="417,-2857.5 417,-2887.5 603,-2887.5 603,-2857.5 417,-2857.5"/>
<text text-anchor="start" x="425" y="-2875.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2864.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxRamLineLastMsb</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node92 -->
<g id="edge93" class="edge"><title>Node0&#45;&gt;Node92</title>
<path fill="none" stroke="midnightblue" d="M276.026,-3752.93C276.588,-3635.81 285.302,-3028.43 405,-2897.5 408.508,-2893.66 412.494,-2890.37 416.809,-2887.54"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.526,-3752.95 275.993,-3762.96 279.526,-3752.97 272.526,-3752.95"/>
</g>
<!-- Node93 -->
<g id="node94" class="node"><title>Node93</title>
<g id="a_node94"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_ram_lsbs.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxRamLsbs">
<polygon fill="white" stroke="black" points="434,-2808.5 434,-2838.5 586,-2838.5 586,-2808.5 434,-2808.5"/>
<text text-anchor="start" x="442" y="-2826.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2815.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxRamLsbs</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node93 -->
<g id="edge94" class="edge"><title>Node0&#45;&gt;Node93</title>
<path fill="none" stroke="midnightblue" d="M275.859,-3752.98C275.389,-3632.2 278.877,-2987.52 405,-2848.5 412.798,-2839.9 422.988,-2834.02 433.936,-2830.02"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.359,-3753.01 275.912,-3762.99 279.359,-3752.97 272.359,-3753.01"/>
</g>
<!-- Node94 -->
<g id="node95" class="node"><title>Node94</title>
<g id="a_node95"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_ram_write_offset_msb.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxRamWriteOffsetMsb">
<polygon fill="white" stroke="black" points="410,-2759.5 410,-2789.5 610,-2789.5 610,-2759.5 410,-2759.5"/>
<text text-anchor="start" x="418" y="-2777.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2766.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxRamWriteOffsetMsb</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node94 -->
<g id="edge95" class="edge"><title>Node0&#45;&gt;Node94</title>
<path fill="none" stroke="midnightblue" d="M275.705,-3752.73C274.249,-3627.61 272.548,-2946.51 405,-2799.5 408.356,-2795.78 412.161,-2792.56 416.283,-2789.78"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.206,-3752.83 275.836,-3762.78 279.205,-3752.74 272.206,-3752.83"/>
</g>
<!-- Node95 -->
<g id="node96" class="node"><title>Node95</title>
<g id="a_node96"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_reset_counters.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxResetCounters">
<polygon fill="white" stroke="black" points="422,-2710.5 422,-2740.5 598,-2740.5 598,-2710.5 422,-2710.5"/>
<text text-anchor="start" x="430" y="-2728.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2717.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxResetCounters</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node95 -->
<g id="edge96" class="edge"><title>Node0&#45;&gt;Node95</title>
<path fill="none" stroke="midnightblue" d="M277.155,-3752.76C285.254,-3617.33 335.235,-2828.42 405,-2750.5 409.831,-2745.1 415.603,-2740.77 421.919,-2737.31"/>
<polygon fill="midnightblue" stroke="midnightblue" points="273.652,-3752.72 276.554,-3762.91 280.64,-3753.13 273.652,-3752.72"/>
</g>
<!-- Node96 -->
<g id="node97" class="node"><title>Node96</title>
<g id="a_node97"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_wait.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.RxWait">
<polygon fill="white" stroke="black" points="437.5,-2661.5 437.5,-2691.5 582.5,-2691.5 582.5,-2661.5 437.5,-2661.5"/>
<text text-anchor="start" x="445.5" y="-2679.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2668.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.RxWait</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node96 -->
<g id="edge97" class="edge"><title>Node0&#45;&gt;Node96</title>
<path fill="none" stroke="midnightblue" d="M277.041,-3752.68C284.532,-3613.3 332.025,-2783.48 405,-2701.5 413.581,-2691.86 425.169,-2685.61 437.497,-2681.62"/>
<polygon fill="midnightblue" stroke="midnightblue" points="273.54,-3752.6 276.504,-3762.77 280.53,-3752.97 273.54,-3752.6"/>
</g>
<!-- Node97 -->
<g id="node98" class="node"><title>Node97</title>
<g id="a_node98"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_sampler_preset_lsb.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SamplerPresetLsb">
<polygon fill="white" stroke="black" points="421.5,-2612.5 421.5,-2642.5 598.5,-2642.5 598.5,-2612.5 421.5,-2612.5"/>
<text text-anchor="start" x="429.5" y="-2630.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2619.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SamplerPresetLsb</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node97 -->
<g id="edge98" class="edge"><title>Node0&#45;&gt;Node97</title>
<path fill="none" stroke="midnightblue" d="M276.932,-3752.68C283.828,-3609.64 328.798,-2738.55 405,-2652.5 409.678,-2647.22 415.259,-2642.95 421.372,-2639.52"/>
<polygon fill="midnightblue" stroke="midnightblue" points="273.421,-3752.82 276.441,-3762.98 280.413,-3753.16 273.421,-3752.82"/>
</g>
<!-- Node98 -->
<g id="node99" class="node"><title>Node98</title>
<g id="a_node99"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_sampler_preset_msb.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SamplerPresetMsb">
<polygon fill="white" stroke="black" points="420,-2563.5 420,-2593.5 600,-2593.5 600,-2563.5 420,-2563.5"/>
<text text-anchor="start" x="428" y="-2581.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2570.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SamplerPresetMsb</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node98 -->
<g id="edge99" class="edge"><title>Node0&#45;&gt;Node98</title>
<path fill="none" stroke="midnightblue" d="M276.83,-3752.72C283.148,-3606.25 325.559,-2693.64 405,-2603.5 409.297,-2598.62 414.364,-2594.61 419.908,-2591.32"/>
<polygon fill="midnightblue" stroke="midnightblue" points="273.325,-3752.76 276.396,-3762.9 280.319,-3753.06 273.325,-3752.76"/>
</g>
<!-- Node99 -->
<g id="node100" class="node"><title>Node99</title>
<g id="a_node100"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_smpl_mode.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SmplMode">
<polygon fill="white" stroke="black" points="437.5,-2514.5 437.5,-2544.5 582.5,-2544.5 582.5,-2514.5 437.5,-2514.5"/>
<text text-anchor="start" x="445.5" y="-2532.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2521.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SmplMode</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node99 -->
<g id="edge100" class="edge"><title>Node0&#45;&gt;Node99</title>
<path fill="none" stroke="midnightblue" d="M276.734,-3752.81C282.489,-3603.19 322.306,-2648.75 405,-2554.5 413.512,-2544.8 425.067,-2538.52 437.387,-2534.53"/>
<polygon fill="midnightblue" stroke="midnightblue" points="273.236,-3752.71 276.354,-3762.84 280.231,-3752.98 273.236,-3752.71"/>
</g>
<!-- Node100 -->
<g id="node101" class="node"><title>Node100</title>
<g id="a_node101"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_config.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiConfig">
<polygon fill="white" stroke="black" points="437.5,-2465.5 437.5,-2495.5 582.5,-2495.5 582.5,-2465.5 437.5,-2465.5"/>
<text text-anchor="start" x="445.5" y="-2483.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2472.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiConfig</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node100 -->
<g id="edge101" class="edge"><title>Node0&#45;&gt;Node100</title>
<path fill="none" stroke="midnightblue" d="M276.661,-3752.5C281.925,-3598.45 319.126,-2603.77 405,-2505.5 413.492,-2495.78 425.039,-2489.5 437.355,-2485.5"/>
<polygon fill="midnightblue" stroke="midnightblue" points="273.152,-3752.69 276.314,-3762.8 280.149,-3752.92 273.152,-3752.69"/>
</g>
<!-- Node101 -->
<g id="node102" class="node"><title>Node101</title>
<g id="a_node102"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_config_pif.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiConfigPif">
<polygon fill="white" stroke="black" points="434,-2416.5 434,-2446.5 586,-2446.5 586,-2416.5 434,-2416.5"/>
<text text-anchor="start" x="442" y="-2434.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2423.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiConfigPif</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node101 -->
<g id="edge102" class="edge"><title>Node0&#45;&gt;Node101</title>
<path fill="none" stroke="midnightblue" d="M276.575,-3752.7C281.304,-3595.94 315.849,-2558.91 405,-2456.5 412.682,-2447.68 422.881,-2441.68 433.893,-2437.66"/>
<polygon fill="midnightblue" stroke="midnightblue" points="273.074,-3752.68 276.276,-3762.78 280.071,-3752.89 273.074,-3752.68"/>
</g>
<!-- Node102 -->
<g id="node103" class="node"><title>Node102</title>
<g id="a_node103"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_idle.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMasterIdle">
<polygon fill="white" stroke="black" points="431.5,-2367.5 431.5,-2397.5 588.5,-2397.5 588.5,-2367.5 431.5,-2367.5"/>
<text text-anchor="start" x="439.5" y="-2385.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2374.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiMasterIdle</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node102 -->
<g id="edge103" class="edge"><title>Node0&#45;&gt;Node102</title>
<path fill="none" stroke="midnightblue" d="M276.509,-3752.46C280.767,-3591.61 312.651,-2513.96 405,-2407.5 412.058,-2399.36 421.256,-2393.63 431.262,-2389.63"/>
<polygon fill="midnightblue" stroke="midnightblue" points="273.001,-3752.7 276.24,-3762.79 279.999,-3752.88 273.001,-3752.7"/>
</g>
<!-- Node103 -->
<g id="node104" class="node"><title>Node103</title>
<g id="a_node104"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_mode.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMasterMode">
<polygon fill="white" stroke="black" points="427.5,-2318.5 427.5,-2348.5 592.5,-2348.5 592.5,-2318.5 427.5,-2318.5"/>
<text text-anchor="start" x="435.5" y="-2336.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2325.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiMasterMode</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node103 -->
<g id="edge104" class="edge"><title>Node0&#45;&gt;Node103</title>
<path fill="none" stroke="midnightblue" d="M276.433,-3752.75C280.183,-3589.67 309.349,-2469.12 405,-2358.5 411.134,-2351.41 418.894,-2346.14 427.402,-2342.25"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.933,-3752.73 276.206,-3762.81 279.931,-3752.89 272.933,-3752.73"/>
</g>
<!-- Node104 -->
<g id="node105" class="node"><title>Node104</title>
<g id="a_node105"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_radar_burst_kick.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMasterRadarBurstKick">
<polygon fill="white" stroke="black" points="405,-2269.5 405,-2299.5 615,-2299.5 615,-2269.5 405,-2269.5"/>
<text text-anchor="start" x="413" y="-2287.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2276.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiMasterRadarBurstKick</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node104 -->
<g id="edge105" class="edge"><title>Node0&#45;&gt;Node104</title>
<path fill="none" stroke="midnightblue" d="M276.373,-3752.58C279.674,-3585.74 306.134,-2424.19 405,-2309.5 408.274,-2305.7 412.014,-2302.43 416.085,-2299.61"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.869,-3752.78 276.175,-3762.85 279.867,-3752.92 272.869,-3752.78"/>
</g>
<!-- Node105 -->
<g id="node106" class="node"><title>Node105</title>
<g id="a_node106"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_radar_burst_size_lsb.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMasterRadarBurst\lSizeLsb">
<polygon fill="white" stroke="black" points="414.5,-2210 414.5,-2251 605.5,-2251 605.5,-2210 414.5,-2210"/>
<text text-anchor="start" x="422.5" y="-2239" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="422.5" y="-2228" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiMasterRadarBurst</text>
<text text-anchor="middle" x="510" y="-2217" font-family="Helvetica,sans-Serif" font-size="10.00">SizeLsb</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node105 -->
<g id="edge106" class="edge"><title>Node0&#45;&gt;Node105</title>
<path fill="none" stroke="midnightblue" d="M276.352,-3752.73C279.532,-3583.5 305.555,-2381.6 405,-2260.5 407.862,-2257.01 411.102,-2253.92 414.623,-2251.17"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.849,-3752.86 276.165,-3762.92 279.848,-3752.99 272.849,-3752.86"/>
</g>
<!-- Node106 -->
<g id="node107" class="node"><title>Node106</title>
<g id="a_node107"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_send.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMasterSend">
<polygon fill="white" stroke="black" points="428,-2161.5 428,-2191.5 592,-2191.5 592,-2161.5 428,-2161.5"/>
<text text-anchor="start" x="436" y="-2179.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2168.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiMasterSend</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node106 -->
<g id="edge107" class="edge"><title>Node0&#45;&gt;Node106</title>
<path fill="none" stroke="midnightblue" d="M276.235,-3752.71C278.471,-3579.44 298.31,-2323.89 405,-2200.5 411.242,-2193.28 419.181,-2188 427.882,-2184.16"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.733,-3752.87 276.108,-3762.91 279.733,-3752.96 272.733,-3752.87"/>
</g>
<!-- Node107 -->
<g id="node108" class="node"><title>Node107</title>
<g id="a_node108"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mb_clear_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMbClearStatus">
<polygon fill="white" stroke="black" points="422,-2112.5 422,-2142.5 598,-2142.5 598,-2112.5 422,-2112.5"/>
<text text-anchor="start" x="430" y="-2130.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2119.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiMbClearStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node107 -->
<g id="edge108" class="edge"><title>Node0&#45;&gt;Node107</title>
<path fill="none" stroke="midnightblue" d="M276.196,-3752.38C278.107,-3574.92 295.754,-2280.33 405,-2152.5 409.705,-2146.99 415.391,-2142.59 421.648,-2139.07"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.694,-3752.59 276.091,-3762.62 279.694,-3752.66 272.694,-3752.59"/>
</g>
<!-- Node108 -->
<g id="node109" class="node"><title>Node108</title>
<g id="a_node109"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mb_fifo_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMbFifoStatus">
<polygon fill="white" stroke="black" points="425,-2063.5 425,-2093.5 595,-2093.5 595,-2063.5 425,-2063.5"/>
<text text-anchor="start" x="433" y="-2081.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2070.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiMbFifoStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node108 -->
<g id="edge109" class="edge"><title>Node0&#45;&gt;Node108</title>
<path fill="none" stroke="midnightblue" d="M276.145,-3752.6C277.631,-3573.03 292.452,-2235.5 405,-2103.5 410.422,-2097.14 417.151,-2092.25 424.56,-2088.49"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.644,-3752.7 276.066,-3762.73 279.644,-3752.76 272.644,-3752.7"/>
</g>
<!-- Node109 -->
<g id="node110" class="node"><title>Node109</title>
<g id="a_node110"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mem_clear_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMemClearStatus">
<polygon fill="white" stroke="black" points="417.5,-2014.5 417.5,-2044.5 602.5,-2044.5 602.5,-2014.5 417.5,-2014.5"/>
<text text-anchor="start" x="425.5" y="-2032.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-2021.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiMemClearStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node109 -->
<g id="edge110" class="edge"><title>Node0&#45;&gt;Node109</title>
<path fill="none" stroke="midnightblue" d="M276.099,-3752.58C277.184,-3570.01 289.197,-2190.62 405,-2054.5 408.61,-2050.26 412.802,-2046.66 417.39,-2043.63"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.598,-3752.83 276.042,-3762.85 279.598,-3752.87 272.598,-3752.83"/>
</g>
<!-- Node110 -->
<g id="node111" class="node"><title>Node110</title>
<g id="a_node111"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mem_fifo_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMemFifoStatus">
<polygon fill="white" stroke="black" points="421,-1965.5 421,-1995.5 599,-1995.5 599,-1965.5 421,-1965.5"/>
<text text-anchor="start" x="429" y="-1983.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-1972.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiMemFifoStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node110 -->
<g id="edge111" class="edge"><title>Node0&#45;&gt;Node110</title>
<path fill="none" stroke="midnightblue" d="M276.054,-3752.87C276.739,-3568.57 285.876,-2145.82 405,-2005.5 409.447,-2000.26 414.781,-1996.02 420.652,-1992.58"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.554,-3752.98 276.021,-3762.99 279.554,-3753 272.554,-3752.98"/>
</g>
<!-- Node111 -->
<g id="node112" class="node"><title>Node111</title>
<g id="a_node112"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data0_clear_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiRadarData0Clear\lStatus">
<polygon fill="white" stroke="black" points="416.5,-1906 416.5,-1947 603.5,-1947 603.5,-1906 416.5,-1906"/>
<text text-anchor="start" x="424.5" y="-1935" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="424.5" y="-1924" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiRadarData0Clear</text>
<text text-anchor="middle" x="510" y="-1913" font-family="Helvetica,sans-Serif" font-size="10.00">Status</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node111 -->
<g id="edge112" class="edge"><title>Node0&#45;&gt;Node111</title>
<path fill="none" stroke="midnightblue" d="M276.055,-3752.64C276.746,-3564.74 285.945,-2103.59 405,-1956.5 408.31,-1952.41 412.141,-1948.86 416.338,-1945.77"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.555,-3752.73 276.022,-3762.74 279.555,-3752.76 272.555,-3752.73"/>
</g>
<!-- Node112 -->
<g id="node113" class="node"><title>Node112</title>
<g id="a_node113"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data0_fifo_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiRadarData0Fifo\lStatus">
<polygon fill="white" stroke="black" points="420,-1846 420,-1887 600,-1887 600,-1846 420,-1846"/>
<text text-anchor="start" x="428" y="-1875" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="428" y="-1864" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiRadarData0Fifo</text>
<text text-anchor="middle" x="510" y="-1853" font-family="Helvetica,sans-Serif" font-size="10.00">Status</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node112 -->
<g id="edge113" class="edge"><title>Node0&#45;&gt;Node112</title>
<path fill="none" stroke="midnightblue" d="M276.007,-3752.58C276.251,-3560.98 282.077,-2048.72 405,-1896.5 409.132,-1891.38 414.078,-1887.1 419.539,-1883.53"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.507,-3752.85 275.998,-3762.85 279.507,-3752.85 272.507,-3752.85"/>
</g>
<!-- Node113 -->
<g id="node114" class="node"><title>Node113</title>
<g id="a_node114"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data1_clear_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiRadarData1Clear\lStatus">
<polygon fill="white" stroke="black" points="416.5,-1786 416.5,-1827 603.5,-1827 603.5,-1786 416.5,-1786"/>
<text text-anchor="start" x="424.5" y="-1815" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="424.5" y="-1804" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiRadarData1Clear</text>
<text text-anchor="middle" x="510" y="-1793" font-family="Helvetica,sans-Serif" font-size="10.00">Status</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node113 -->
<g id="edge114" class="edge"><title>Node0&#45;&gt;Node113</title>
<path fill="none" stroke="midnightblue" d="M275.963,-3752.83C275.768,-3558.98 278.138,-1993.94 405,-1836.5 408.301,-1832.4 412.125,-1828.84 416.316,-1825.75"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.463,-3752.98 275.977,-3762.97 279.463,-3752.97 272.463,-3752.98"/>
</g>
<!-- Node114 -->
<g id="node115" class="node"><title>Node114</title>
<g id="a_node115"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data1_fifo_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiRadarData1Fifo\lStatus">
<polygon fill="white" stroke="black" points="420,-1726 420,-1767 600,-1767 600,-1726 420,-1726"/>
<text text-anchor="start" x="428" y="-1755" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="428" y="-1744" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiRadarData1Fifo</text>
<text text-anchor="middle" x="510" y="-1733" font-family="Helvetica,sans-Serif" font-size="10.00">Status</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node114 -->
<g id="edge115" class="edge"><title>Node0&#45;&gt;Node114</title>
<path fill="none" stroke="midnightblue" d="M275.92,-3752.52C275.302,-3553.98 274.32,-1939.01 405,-1776.5 409.239,-1771.23 414.343,-1766.85 419.984,-1763.21"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.421,-3752.69 275.956,-3762.68 279.421,-3752.67 272.421,-3752.69"/>
</g>
<!-- Node115 -->
<g id="node116" class="node"><title>Node115</title>
<g id="a_node116"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data_clear_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiRadarDataClear\lStatus">
<polygon fill="white" stroke="black" points="419,-1666 419,-1707 601,-1707 601,-1666 419,-1666"/>
<text text-anchor="start" x="427" y="-1695" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="start" x="427" y="-1684" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SpiRadarDataClear</text>
<text text-anchor="middle" x="510" y="-1673" font-family="Helvetica,sans-Serif" font-size="10.00">Status</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node115 -->
<g id="edge116" class="edge"><title>Node0&#45;&gt;Node115</title>
<path fill="none" stroke="midnightblue" d="M275.881,-3752.54C274.848,-3550.82 270.428,-1884.18 405,-1716.5 408.881,-1711.66 413.49,-1707.58 418.575,-1704.12"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.382,-3752.85 275.937,-3762.83 279.382,-3752.81 272.382,-3752.85"/>
</g>
<!-- Node116 -->
<g id="node117" class="node"><title>Node116</title>
<g id="a_node117"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_to_cpu_read_data.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.ToCpuReadData">
<polygon fill="white" stroke="black" points="425,-1617.5 425,-1647.5 595,-1647.5 595,-1617.5 425,-1617.5"/>
<text text-anchor="start" x="433" y="-1635.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-1624.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.ToCpuReadData</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node116 -->
<g id="edge117" class="edge"><title>Node0&#45;&gt;Node116</title>
<path fill="none" stroke="midnightblue" d="M276.543,-3752.28C282.303,-3540.89 333.383,-1741.11 405,-1656.5 410.507,-1649.99 417.393,-1645.05 424.984,-1641.32"/>
<polygon fill="midnightblue" stroke="midnightblue" points="273.037,-3752.46 276.265,-3762.55 280.034,-3752.65 273.037,-3752.46"/>
</g>
<!-- Node117 -->
<g id="node118" class="node"><title>Node117</title>
<g id="a_node118"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_to_cpu_write_data.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.ToCpuWriteData">
<polygon fill="white" stroke="black" points="425,-1568.5 425,-1598.5 595,-1598.5 595,-1568.5 425,-1568.5"/>
<text text-anchor="start" x="433" y="-1586.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-1575.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.ToCpuWriteData</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node117 -->
<g id="edge118" class="edge"><title>Node0&#45;&gt;Node117</title>
<path fill="none" stroke="midnightblue" d="M276.514,-3752.48C282.064,-3539.43 332.144,-1695.49 405,-1608.5 410.485,-1601.95 417.356,-1596.95 424.939,-1593.15"/>
<polygon fill="midnightblue" stroke="midnightblue" points="273.007,-3752.71 276.247,-3762.8 280.004,-3752.89 273.007,-3752.71"/>
</g>
<!-- Node118 -->
<g id="node119" class="node"><title>Node118</title>
<g id="a_node119"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_to_mem_write_data.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.ToMemWriteData">
<polygon fill="white" stroke="black" points="423,-1519.5 423,-1549.5 597,-1549.5 597,-1519.5 423,-1519.5"/>
<text text-anchor="start" x="431" y="-1537.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-1526.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.ToMemWriteData</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node118 -->
<g id="edge119" class="edge"><title>Node0&#45;&gt;Node118</title>
<path fill="none" stroke="midnightblue" d="M276.49,-3752.36C281.832,-3536.03 330.521,-1648.54 405,-1559.5 410.004,-1553.52 416.165,-1548.83 422.97,-1545.16"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.985,-3752.49 276.239,-3762.58 279.983,-3752.67 272.985,-3752.49"/>
</g>
<!-- Node119 -->
<g id="node120" class="node"><title>Node119</title>
<g id="a_node120"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_backend_done.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxBackendDone">
<polygon fill="white" stroke="black" points="423.5,-1470.5 423.5,-1500.5 596.5,-1500.5 596.5,-1470.5 423.5,-1470.5"/>
<text text-anchor="start" x="431.5" y="-1488.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-1477.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxBackendDone</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node119 -->
<g id="edge120" class="edge"><title>Node0&#45;&gt;Node119</title>
<path fill="none" stroke="midnightblue" d="M276.458,-3752.58C281.554,-3534.73 328.858,-1601.65 405,-1510.5 410.119,-1504.37 416.453,-1499.6 423.449,-1495.89"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.953,-3752.76 276.22,-3762.84 279.952,-3752.92 272.953,-3752.76"/>
</g>
<!-- Node120 -->
<g id="node121" class="node"><title>Node120</title>
<g id="a_node121"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_clocks_per_pulse.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxClocksPerPulse">
<polygon fill="white" stroke="black" points="419,-1421.5 419,-1451.5 601,-1451.5 601,-1421.5 419,-1421.5"/>
<text text-anchor="start" x="427" y="-1439.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-1428.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxClocksPerPulse</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node120 -->
<g id="edge121" class="edge"><title>Node0&#45;&gt;Node120</title>
<path fill="none" stroke="midnightblue" d="M276.436,-3752.48C281.329,-3531.55 327.231,-1554.71 405,-1461.5 408.926,-1456.79 413.568,-1452.89 418.676,-1449.65"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.934,-3752.56 276.213,-3762.63 279.932,-3752.71 272.934,-3752.56"/>
</g>
<!-- Node121 -->
<g id="node122" class="node"><title>Node121</title>
<g id="a_node122"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_ctrl_done.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxCtrlDone">
<polygon fill="white" stroke="black" points="435,-1372.5 435,-1402.5 585,-1402.5 585,-1372.5 435,-1372.5"/>
<text text-anchor="start" x="443" y="-1390.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-1379.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxCtrlDone</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node121 -->
<g id="edge122" class="edge"><title>Node0&#45;&gt;Node121</title>
<path fill="none" stroke="midnightblue" d="M276.407,-3752.74C281.061,-3530.55 325.561,-1507.82 405,-1412.5 412.787,-1403.16 423.397,-1396.96 434.877,-1392.91"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.904,-3752.84 276.195,-3762.91 279.902,-3752.99 272.904,-3752.84"/>
</g>
<!-- Node122 -->
<g id="node123" class="node"><title>Node122</title>
<g id="a_node123"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_ctrl_mode.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxCtrlMode">
<polygon fill="white" stroke="black" points="434.5,-1323.5 434.5,-1353.5 585.5,-1353.5 585.5,-1323.5 434.5,-1323.5"/>
<text text-anchor="start" x="442.5" y="-1341.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-1330.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxCtrlMode</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node122 -->
<g id="edge123" class="edge"><title>Node0&#45;&gt;Node122</title>
<path fill="none" stroke="midnightblue" d="M276.386,-3752.66C280.843,-3527.58 323.931,-1460.89 405,-1363.5 412.663,-1354.29 423.066,-1348.14 434.343,-1344.09"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.885,-3752.65 276.188,-3762.71 279.884,-3752.79 272.885,-3752.65"/>
</g>
<!-- Node123 -->
<g id="node124" class="node"><title>Node123</title>
<g id="a_node124"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_max_h.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacMaxH">
<polygon fill="white" stroke="black" points="432.5,-1274.5 432.5,-1304.5 587.5,-1304.5 587.5,-1274.5 432.5,-1274.5"/>
<text text-anchor="start" x="440.5" y="-1292.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-1281.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxDacMaxH</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node123 -->
<g id="edge124" class="edge"><title>Node0&#45;&gt;Node123</title>
<path fill="none" stroke="midnightblue" d="M276.371,-3752.26C280.67,-3522.6 322.339,-1413.91 405,-1314.5 412.183,-1305.86 421.779,-1299.91 432.247,-1295.86"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.867,-3752.45 276.181,-3762.52 279.866,-3752.58 272.867,-3752.45"/>
</g>
<!-- Node124 -->
<g id="node125" class="node"><title>Node124</title>
<g id="a_node125"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_max_l.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacMaxL">
<polygon fill="white" stroke="black" points="433.5,-1225.5 433.5,-1255.5 586.5,-1255.5 586.5,-1225.5 433.5,-1225.5"/>
<text text-anchor="start" x="441.5" y="-1243.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-1232.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxDacMaxL</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node124 -->
<g id="edge125" class="edge"><title>Node0&#45;&gt;Node124</title>
<path fill="none" stroke="midnightblue" d="M276.345,-3752.57C280.415,-3522 320.662,-1367.03 405,-1265.5 412.416,-1256.57 422.41,-1250.51 433.281,-1246.46"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.841,-3752.77 276.165,-3762.83 279.84,-3752.89 272.841,-3752.77"/>
</g>
<!-- Node125 -->
<g id="node126" class="node"><title>Node125</title>
<g id="a_node126"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_min_h.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacMinH">
<polygon fill="white" stroke="black" points="434,-1176.5 434,-1206.5 586,-1206.5 586,-1176.5 434,-1176.5"/>
<text text-anchor="start" x="442" y="-1194.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-1183.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxDacMinH</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node125 -->
<g id="edge126" class="edge"><title>Node0&#45;&gt;Node125</title>
<path fill="none" stroke="midnightblue" d="M276.319,-3752.88C280.166,-3521.52 318.983,-1320.16 405,-1216.5 412.53,-1207.43 422.723,-1201.31 433.795,-1197.26"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.82,-3752.84 276.154,-3762.89 279.819,-3752.95 272.82,-3752.84"/>
</g>
<!-- Node126 -->
<g id="node127" class="node"><title>Node126</title>
<g id="a_node127"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_min_l.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacMinL">
<polygon fill="white" stroke="black" points="435,-1127.5 435,-1157.5 585,-1157.5 585,-1127.5 435,-1127.5"/>
<text text-anchor="start" x="443" y="-1145.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-1134.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxDacMinL</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node126 -->
<g id="edge127" class="edge"><title>Node0&#45;&gt;Node126</title>
<path fill="none" stroke="midnightblue" d="M276.301,-3752.86C279.96,-3518.98 317.344,-1273.24 405,-1167.5 412.763,-1158.14 423.362,-1151.93 434.837,-1147.87"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.799,-3752.91 276.144,-3762.96 279.799,-3753.02 272.799,-3752.91"/>
</g>
<!-- Node127 -->
<g id="node128" class="node"><title>Node127</title>
<g id="a_node128"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_override_h.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacOverrideH">
<polygon fill="white" stroke="black" points="423.5,-1078.5 423.5,-1108.5 596.5,-1108.5 596.5,-1078.5 423.5,-1078.5"/>
<text text-anchor="start" x="431.5" y="-1096.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-1085.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxDacOverrideH</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node127 -->
<g id="edge128" class="edge"><title>Node0&#45;&gt;Node127</title>
<path fill="none" stroke="midnightblue" d="M276.288,-3752.49C279.794,-3514.22 315.748,-1226.26 405,-1118.5 410.093,-1112.35 416.41,-1107.56 423.395,-1103.85"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.784,-3752.73 276.138,-3762.78 279.783,-3752.84 272.784,-3752.73"/>
</g>
<!-- Node128 -->
<g id="node129" class="node"><title>Node128</title>
<g id="a_node129"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_override_l.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacOverrideL">
<polygon fill="white" stroke="black" points="424.5,-1029.5 424.5,-1059.5 595.5,-1059.5 595.5,-1029.5 424.5,-1029.5"/>
<text text-anchor="start" x="432.5" y="-1047.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-1036.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxDacOverrideL</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node128 -->
<g id="edge129" class="edge"><title>Node0&#45;&gt;Node128</title>
<path fill="none" stroke="midnightblue" d="M276.265,-3752.85C279.558,-3514.13 314.061,-1179.4 405,-1069.5 410.327,-1063.06 416.996,-1058.11 424.368,-1054.33"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.765,-3752.82 276.128,-3762.87 279.764,-3752.92 272.765,-3752.82"/>
</g>
<!-- Node129 -->
<g id="node130" class="node"><title>Node129</title>
<g id="a_node130"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_override_load.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacOverrideLoad">
<polygon fill="white" stroke="black" points="416.5,-980.5 416.5,-1010.5 603.5,-1010.5 603.5,-980.5 416.5,-980.5"/>
<text text-anchor="start" x="424.5" y="-998.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-987.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxDacOverrideLoad</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node129 -->
<g id="edge130" class="edge"><title>Node0&#45;&gt;Node129</title>
<path fill="none" stroke="midnightblue" d="M276.247,-3752.86C279.361,-3511.86 312.417,-1132.48 405,-1020.5 408.313,-1016.49 412.146,-1013.06 416.344,-1010.13"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.746,-3752.91 276.118,-3762.96 279.746,-3753 272.746,-3752.91"/>
</g>
<!-- Node130 -->
<g id="node131" class="node"><title>Node130</title>
<g id="a_node131"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_step.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacStep">
<polygon fill="white" stroke="black" points="435.5,-931.5 435.5,-961.5 584.5,-961.5 584.5,-931.5 435.5,-931.5"/>
<text text-anchor="start" x="443.5" y="-949.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-938.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxDacStep</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node130 -->
<g id="edge131" class="edge"><title>Node0&#45;&gt;Node130</title>
<path fill="none" stroke="midnightblue" d="M276.235,-3752.51C279.199,-3507.22 310.818,-1085.51 405,-971.5 412.865,-961.979 423.661,-955.718 435.336,-951.665"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.732,-3752.75 276.112,-3762.79 279.731,-3752.83 272.732,-3752.75"/>
</g>
<!-- Node131 -->
<g id="node132" class="node"><title>Node131</title>
<g id="a_node132"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_iterations.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxIterations">
<polygon fill="white" stroke="black" points="434,-882.5 434,-912.5 586,-912.5 586,-882.5 434,-882.5"/>
<text text-anchor="start" x="442" y="-900.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-889.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxIterations</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node131 -->
<g id="edge132" class="edge"><title>Node0&#45;&gt;Node131</title>
<path fill="none" stroke="midnightblue" d="M276.218,-3752.53C279.007,-3505.08 309.172,-1038.59 405,-922.5 412.507,-913.406 422.688,-907.285 433.755,-903.222"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.715,-3752.85 276.103,-3762.89 279.714,-3752.93 272.715,-3752.85"/>
</g>
<!-- Node132 -->
<g id="node133" class="node"><title>Node132</title>
<g id="a_node133"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_lfsr_reset.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxLfsrReset">
<polygon fill="white" stroke="black" points="433,-833.5 433,-863.5 587,-863.5 587,-833.5 433,-833.5"/>
<text text-anchor="start" x="441" y="-851.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-840.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxLfsrReset</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node132 -->
<g id="edge133" class="edge"><title>Node0&#45;&gt;Node132</title>
<path fill="none" stroke="midnightblue" d="M276.198,-3752.94C278.788,-3505.52 307.475,-991.742 405,-873.5 412.326,-864.618 422.203,-858.572 432.967,-854.509"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.698,-3752.96 276.095,-3762.99 279.698,-3753.03 272.698,-3752.96"/>
</g>
<!-- Node133 -->
<g id="node134" class="node"><title>Node133</title>
<g id="a_node134"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_lfsr_taps0.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxLfsrTaps0">
<polygon fill="white" stroke="black" points="432.5,-784.5 432.5,-814.5 587.5,-814.5 587.5,-784.5 432.5,-784.5"/>
<text text-anchor="start" x="440.5" y="-802.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-791.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxLfsrTaps0</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node133 -->
<g id="edge134" class="edge"><title>Node0&#45;&gt;Node133</title>
<path fill="none" stroke="midnightblue" d="M276.187,-3752.61C278.631,-3501.01 305.874,-944.773 405,-824.5 412.204,-815.759 421.88,-809.764 432.441,-805.702"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.685,-3752.8 276.089,-3762.83 279.685,-3752.87 272.685,-3752.8"/>
</g>
<!-- Node134 -->
<g id="node135" class="node"><title>Node134</title>
<g id="a_node135"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_lfsr_taps1.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxLfsrTaps1">
<polygon fill="white" stroke="black" points="432.5,-735.5 432.5,-765.5 587.5,-765.5 587.5,-735.5 432.5,-735.5"/>
<text text-anchor="start" x="440.5" y="-753.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-742.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxLfsrTaps1</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node134 -->
<g id="edge135" class="edge"><title>Node0&#45;&gt;Node134</title>
<path fill="none" stroke="midnightblue" d="M276.172,-3752.67C278.447,-3499.13 304.222,-897.866 405,-775.5 412.201,-766.756 421.876,-760.76 432.436,-756.698"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.669,-3752.92 276.081,-3762.95 279.669,-3752.98 272.669,-3752.92"/>
</g>
<!-- Node135 -->
<g id="node136" class="node"><title>Node135</title>
<g id="a_node136"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_lfsr_taps2.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxLfsrTaps2">
<polygon fill="white" stroke="black" points="432.5,-686.5 432.5,-716.5 587.5,-716.5 587.5,-686.5 432.5,-686.5"/>
<text text-anchor="start" x="440.5" y="-704.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-693.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxLfsrTaps2</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node135 -->
<g id="edge136" class="edge"><title>Node0&#45;&gt;Node135</title>
<path fill="none" stroke="midnightblue" d="M276.157,-3752.72C278.266,-3497.28 302.57,-850.96 405,-726.5 412.198,-717.754 421.871,-711.756 432.43,-707.694"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.657,-3752.76 276.075,-3762.79 279.656,-3752.82 272.657,-3752.76"/>
</g>
<!-- Node136 -->
<g id="node137" class="node"><title>Node136</title>
<g id="a_node137"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_pulses_per_step_lsb.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxPulsesPerStepLsb">
<polygon fill="white" stroke="black" points="414,-637.5 414,-667.5 606,-667.5 606,-637.5 414,-637.5"/>
<text text-anchor="start" x="422" y="-655.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-644.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxPulsesPerStepLsb</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node136 -->
<g id="edge137" class="edge"><title>Node0&#45;&gt;Node136</title>
<path fill="none" stroke="midnightblue" d="M276.143,-3752.8C278.089,-3495.58 300.915,-804.057 405,-677.5 408.067,-673.771 411.584,-670.542 415.427,-667.749"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.642,-3752.89 276.068,-3762.92 279.642,-3752.94 272.642,-3752.89"/>
</g>
<!-- Node137 -->
<g id="node138" class="node"><title>Node137</title>
<g id="a_node138"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_pulses_per_step_msb.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxPulsesPerStepMsb">
<polygon fill="white" stroke="black" points="412.5,-588.5 412.5,-618.5 607.5,-618.5 607.5,-588.5 412.5,-588.5"/>
<text text-anchor="start" x="420.5" y="-606.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-595.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxPulsesPerStepMsb</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node137 -->
<g id="edge138" class="edge"><title>Node0&#45;&gt;Node137</title>
<path fill="none" stroke="midnightblue" d="M276.132,-3752.49C277.935,-3491.2 299.311,-757.092 405,-628.5 408.066,-624.77 411.581,-621.54 415.424,-618.746"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.63,-3752.74 276.062,-3762.76 279.63,-3752.79 272.63,-3752.74"/>
</g>
<!-- Node138 -->
<g id="node139" class="node"><title>Node138</title>
<g id="a_node139"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_start.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxStart">
<polygon fill="white" stroke="black" points="437.5,-539.5 437.5,-569.5 582.5,-569.5 582.5,-539.5 437.5,-539.5"/>
<text text-anchor="start" x="445.5" y="-557.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-546.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TrxStart</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node138 -->
<g id="edge139" class="edge"><title>Node0&#45;&gt;Node138</title>
<path fill="none" stroke="midnightblue" d="M276.118,-3752.58C277.762,-3489.62 297.654,-710.192 405,-579.5 413.309,-569.383 424.93,-562.943 437.407,-558.921"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.616,-3752.88 276.055,-3762.9 279.616,-3752.92 272.616,-3752.88"/>
</g>
<!-- Node139 -->
<g id="node140" class="node"><title>Node139</title>
<g id="a_node140"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_ctrl1.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TxPllCtrl1">
<polygon fill="white" stroke="black" points="437.5,-490.5 437.5,-520.5 582.5,-520.5 582.5,-490.5 437.5,-490.5"/>
<text text-anchor="start" x="445.5" y="-508.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-497.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TxPllCtrl1</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node139 -->
<g id="edge140" class="edge"><title>Node0&#45;&gt;Node139</title>
<path fill="none" stroke="midnightblue" d="M276.105,-3752.67C277.591,-3488.08 295.996,-663.294 405,-530.5 413.306,-520.381 424.925,-513.939 437.402,-509.917"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.605,-3752.73 276.05,-3762.75 279.605,-3752.77 272.605,-3752.73"/>
</g>
<!-- Node140 -->
<g id="node141" class="node"><title>Node140</title>
<g id="a_node141"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_ctrl2.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TxPllCtrl2">
<polygon fill="white" stroke="black" points="437.5,-441.5 437.5,-471.5 582.5,-471.5 582.5,-441.5 437.5,-441.5"/>
<text text-anchor="start" x="445.5" y="-459.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-448.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TxPllCtrl2</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node140 -->
<g id="edge141" class="edge"><title>Node0&#45;&gt;Node140</title>
<path fill="none" stroke="midnightblue" d="M276.093,-3752.77C277.424,-3486.68 294.335,-616.398 405,-481.5 413.303,-471.378 424.921,-464.935 437.397,-460.913"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.592,-3752.88 276.043,-3762.89 279.592,-3752.91 272.592,-3752.88"/>
</g>
<!-- Node141 -->
<g id="node142" class="node"><title>Node141</title>
<g id="a_node142"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_skewcalin.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TxPllSkewcalin">
<polygon fill="white" stroke="black" points="428,-392.5 428,-422.5 592,-422.5 592,-392.5 428,-392.5"/>
<text text-anchor="start" x="436" y="-410.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-399.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TxPllSkewcalin</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node141 -->
<g id="edge142" class="edge"><title>Node0&#45;&gt;Node141</title>
<path fill="none" stroke="midnightblue" d="M276.082,-3752.48C277.275,-3482.43 292.729,-569.436 405,-432.5 411.122,-425.033 419.049,-419.567 427.793,-415.599"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.581,-3752.74 276.038,-3762.75 279.581,-3752.77 272.581,-3752.74"/>
</g>
<!-- Node142 -->
<g id="node143" class="node"><title>Node142</title>
<g id="a_node143"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_skew_ctrl.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TxPllSkewCtrl">
<polygon fill="white" stroke="black" points="430,-343.5 430,-373.5 590,-373.5 590,-343.5 430,-343.5"/>
<text text-anchor="start" x="438" y="-361.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-350.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TxPllSkewCtrl</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node142 -->
<g id="edge143" class="edge"><title>Node0&#45;&gt;Node142</title>
<path fill="none" stroke="midnightblue" d="M276.07,-3752.6C277.112,-3481.16 291.065,-522.544 405,-383.5 411.591,-375.456 420.275,-369.736 429.821,-365.706"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.569,-3752.89 276.032,-3762.9 279.569,-3752.91 272.569,-3752.89"/>
</g>
<!-- Node143 -->
<g id="node144" class="node"><title>Node143</title>
<g id="a_node144"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_status.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TxPllStatus">
<polygon fill="white" stroke="black" points="436,-294.5 436,-324.5 584,-324.5 584,-294.5 436,-294.5"/>
<text text-anchor="start" x="444" y="-312.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-301.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TxPllStatus</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node143 -->
<g id="edge144" class="edge"><title>Node0&#45;&gt;Node143</title>
<path fill="none" stroke="midnightblue" d="M276.059,-3752.72C276.952,-3479.92 289.401,-475.653 405,-334.5 412.942,-324.802 423.926,-318.481 435.796,-314.429"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.559,-3752.75 276.027,-3762.76 279.559,-3752.77 272.559,-3752.75"/>
</g>
<!-- Node144 -->
<g id="node145" class="node"><title>Node144</title>
<g id="a_node145"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_wait.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.TxWait">
<polygon fill="white" stroke="black" points="437.5,-245.5 437.5,-275.5 582.5,-275.5 582.5,-245.5 437.5,-245.5"/>
<text text-anchor="start" x="445.5" y="-263.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-252.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.TxWait</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node144 -->
<g id="edge145" class="edge"><title>Node0&#45;&gt;Node144</title>
<path fill="none" stroke="midnightblue" d="M276.048,-3752.85C276.795,-3478.83 287.735,-428.765 405,-285.5 413.292,-275.369 424.905,-268.922 437.38,-264.898"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.548,-3752.91 276.022,-3762.92 279.548,-3752.93 272.548,-3752.91"/>
</g>
<!-- Node145 -->
<g id="node146" class="node"><title>Node145</title>
<g id="a_node146"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_vref_trim.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.VrefTrim">
<polygon fill="white" stroke="black" points="437.5,-196.5 437.5,-226.5 582.5,-226.5 582.5,-196.5 437.5,-196.5"/>
<text text-anchor="start" x="445.5" y="-214.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-203.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.VrefTrim</text>
</a>
</g>
</g>
<!-- Node0&#45;&gt;Node145 -->
<g id="edge146" class="edge"><title>Node0&#45;&gt;Node145</title>
<path fill="none" stroke="midnightblue" d="M276.038,-3752.57C276.65,-3474.71 286.126,-381.806 405,-236.5 413.29,-226.367 424.901,-219.919 437.376,-215.895"/>
<polygon fill="midnightblue" stroke="midnightblue" points="272.538,-3752.78 276.017,-3762.79 279.538,-3752.8 272.538,-3752.78"/>
</g>
<!-- Node156 -->
<g id="node148" class="node"><title>Node156</title>
<g id="a_node148"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_x4.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.X4">
<polygon fill="white" stroke="black" points="437.5,-147.5 437.5,-177.5 582.5,-177.5 582.5,-147.5 437.5,-147.5"/>
<text text-anchor="start" x="445.5" y="-165.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-154.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.X4</text>
</a>
</g>
</g>
<!-- Node155&#45;&gt;Node156 -->
<g id="edge148" class="edge"><title>Node155&#45;&gt;Node156</title>
<path fill="none" stroke="midnightblue" d="M370.299,-162.5C392.657,-162.5 416.147,-162.5 437.31,-162.5"/>
<polygon fill="midnightblue" stroke="midnightblue" points="370.118,-159 360.118,-162.5 370.118,-166 370.118,-159"/>
</g>
<!-- Node158 -->
<g id="node150" class="node"><title>Node158</title>
<g id="a_node150"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_p_i_f.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.PIF">
<polygon fill="white" stroke="black" points="437.5,-98.5 437.5,-128.5 582.5,-128.5 582.5,-98.5 437.5,-98.5"/>
<text text-anchor="start" x="445.5" y="-116.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-105.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.PIF</text>
</a>
</g>
</g>
<!-- Node157&#45;&gt;Node158 -->
<g id="edge150" class="edge"><title>Node157&#45;&gt;Node158</title>
<path fill="none" stroke="midnightblue" d="M370.299,-113.5C392.657,-113.5 416.147,-113.5 437.31,-113.5"/>
<polygon fill="midnightblue" stroke="midnightblue" points="370.118,-110 360.118,-113.5 370.118,-117 370.118,-110"/>
</g>
<!-- Node159 -->
<g id="node151" class="node"><title>Node159</title>
<g id="a_node151"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_s_p_i.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.SPI">
<polygon fill="white" stroke="black" points="437.5,-49.5 437.5,-79.5 582.5,-79.5 582.5,-49.5 437.5,-49.5"/>
<text text-anchor="start" x="445.5" y="-67.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-56.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.SPI</text>
</a>
</g>
</g>
<!-- Node157&#45;&gt;Node159 -->
<g id="edge151" class="edge"><title>Node157&#45;&gt;Node159</title>
<path fill="none" stroke="midnightblue" d="M357.372,-96.3873C383.52,-90.8885 412.272,-84.842 437.644,-79.5062"/>
<polygon fill="midnightblue" stroke="midnightblue" points="356.557,-92.9822 347.491,-98.4654 357.997,-99.8324 356.557,-92.9822"/>
</g>
<!-- Node160 -->
<g id="node152" class="node"><title>Node160</title>
<g id="a_node152"><a xlink:href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_x_i_f.xhtml" target="_top" xlink:title="pymoduleconnector.extras.x4\l_regmap_autogen.XIF">
<polygon fill="white" stroke="black" points="437.5,-0.5 437.5,-30.5 582.5,-30.5 582.5,-0.5 437.5,-0.5"/>
<text text-anchor="start" x="445.5" y="-18.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.extras.x4</text>
<text text-anchor="middle" x="510" y="-7.5" font-family="Helvetica,sans-Serif" font-size="10.00">_regmap_autogen.XIF</text>
</a>
</g>
</g>
<!-- Node157&#45;&gt;Node160 -->
<g id="edge152" class="edge"><title>Node157&#45;&gt;Node160</title>
<path fill="none" stroke="midnightblue" d="M359.836,-94.0216C363.013,-92.3317 366.083,-90.4954 369,-88.5 391.305,-73.2439 382.425,-54.3543 405,-39.5 414.752,-33.0831 426.003,-28.371 437.424,-24.9132"/>
<polygon fill="midnightblue" stroke="midnightblue" points="358.156,-90.9448 350.635,-98.407 361.168,-97.2637 358.156,-90.9448"/>
</g>
</g>
</svg>
