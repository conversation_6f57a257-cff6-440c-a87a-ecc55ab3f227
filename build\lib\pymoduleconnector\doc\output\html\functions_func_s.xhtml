<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_s"></a>- s -</h3><ul>
<li>search_for_file_by_type()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a4e5f0a90b0ea04c1ff527b6b44696857">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>seek_byte()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml#a1b92b46c44725ee2bce909f7b1822fd2">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a>
</li>
<li>seek_ms()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml#a660a5fac2b90e1534a2b49ae3430ffff">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a>
</li>
<li>set_basename_for_data_type()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_recorder.xhtml#aaff29cb2553fd90cad290a951faddd13">pymoduleconnector.moduleconnectorwrapper.PyDataRecorder</a>
</li>
<li>set_baudrate()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#af469293f646b75661793c6c672cd746d">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#afab145f09351f9150531e0f9a4bdac99">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a2bee25ec205a5bf0eb06f356a092cc03">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a5135c26d0cf062e1f94276dc8eeafdcf">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>set_byte_count()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml#ac1cba62a0415affe89efab0a2cedfa6f">pymoduleconnector.moduleconnectorwrapper.PreferredSplitSize</a>
</li>
<li>set_data_rate_limit()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a5f5788127ec56ee7d14ab848299988cb">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>
</li>
<li>set_debug_level()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a39e22df5876a6a6191dc3d442e656573">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a3523741b12df37e1e98e1ad4ef3ef292">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a50e5afe9d7539e47e650a48b4426081e">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a085ffc4ab0640ab87c848059467d003f">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>set_debug_output_control()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a039f80a8eb1df436c985fc524acf9a75">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a02b4d758a3cc6b5a111d55957e6c64a7">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a45b0182d2b879249eb564ea193b2d82e">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>set_decimation_factor()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a16f46be3daea6dbdb1f5cb396250fd5b">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>set_default_timeout()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml#a1dd86516afa232885afeed91bee0b896">pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector</a>
</li>
<li>set_detection_zone()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a3c0f4ead1bf1cc85246e8ad0493c1f27">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a698a48e04a58fb18051a9f3951dd5b28">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#adb588a48e37cce16c1d52d1caa4eb424">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#adbf23c82bc1db62ea2960e16a1355040">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>set_directory_split_size()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#acf02bee9c4142297511c556d635305d0">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>
</li>
<li>set_duration()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml#a3c0621ca2405a4692979fe4f9d648b8a">pymoduleconnector.moduleconnectorwrapper.PreferredSplitSize</a>
</li>
<li>set_file()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aa7090fcdca2bdffd2fa9f06f6636c6d0">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>set_file_data()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a8eebb59248385899c58c6600d148dec4">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>set_file_split_size()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#ad0c2444e6629f7334ae3859ad6946d4b">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>
</li>
<li>set_filter()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_player.xhtml#a146e5b71ee6d4393da6912c31416ca40">pymoduleconnector.moduleconnectorwrapper.PyDataPlayer</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml#a81d35851b4623552ddd2b731f9c7ef55">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a>
</li>
<li>set_fixed_daily_hour()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml#aad4268835a259e9f88617dad82464159">pymoduleconnector.moduleconnectorwrapper.PreferredSplitSize</a>
</li>
<li>set_flush_on_write()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#aed473a5de7c674635120149aea07e3de">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>
</li>
<li>set_iopin_control()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#aa43fb34928200ae7fa011abdf7435a29">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ad0d49f580196866d0aff7b172b6670b0">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a6367c4c3284b0ba6a8dfe2ebba4841d1">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#af1cfd6f8dbeabdf195eea0be7b0c48b4">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>set_iopin_value()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ab1f5de2ba4be6492c16575919ac29d0e">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a24c14611e50da75a7325fd5dba415aa3">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a0bdec90b34c48f9d253461e4eadf3255">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a4f41fd99adcb1f5f46b6f91e7156c30f">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>set_led_control()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#adb0f24e9a448f4feb4699032f57587fa">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a1e433f3f04d447905790085fa880fc3b">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ad5c41bc427410450627c7645e72780ab">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a083234422dfa614a412b9f42b30ac6e5">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>set_legacy_output()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a393504b291fe0913d7d8df8c5e4c0393">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>set_log_level()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml#a46827254fbb052ef5c61389873556673">pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a3eb7c7e255d9691dda635e9a800924b7">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>set_loop_mode_enabled()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_player.xhtml#aa671088ab303d61d1ab43bd5ef5169de">pymoduleconnector.moduleconnectorwrapper.PyDataPlayer</a>
</li>
<li>set_noisemap_control()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a5ba004b644addb6845b21c8b7b96f4e6">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a4c8cecca6e28b061434f6f21cb23b907">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a58bd7bc23687822d42384078ed77d775">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>set_normalization()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a4ad27bc4e4219d3f9da1112808fbd27c">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>set_number_format()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ad12abf8278e658d45807da0fb71a93db">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>set_output_control()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a936abf20cd3178a12b240d57a99eebb0">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a90875143cc49a39ed93944d61a86a4e5">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#afa5f8502b525d703418b815c08c767a4">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>set_parameter_file()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a150edea748f14e6ffdaebbfd8c6b51bb">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a57fe31f4c6aefb920879d829f425ff6b">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ada49c26885531b7f3965c4c006c6a804">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>set_periodic_noisemap_store()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ae442c0b14493f6ecbfef1b83cd305dee">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a1cacd7e63cb2d17842126fb440cc5559">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a07a6814f052efd83fe3d61ce96448375">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>set_phase_noise_correction()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a593f6400a8c812c6582e00ef56709ba0">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>set_playback_rate()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_player.xhtml#a3bf732948214a024ccb10a768427bb29">pymoduleconnector.moduleconnectorwrapper.PyDataPlayer</a>
</li>
<li>set_position()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_player.xhtml#a2843584062fc58d17a91f5c2eb1ea642">pymoduleconnector.moduleconnectorwrapper.PyDataPlayer</a>
</li>
<li>set_register()
: <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_map.xhtml#adfdb69ac38ed2fa3edbcb83e2ce99c0f">pymoduleconnector.extras.regmap.RegMap</a>
</li>
<li>set_sensitivity()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a2718889bd1d8d5fd08fb7764fe27db6c">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a54b8c3fc6865d4f57ed5b2e3fb7bb6ae">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aeed10f4647052d4754a04600dc8b766f">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#afe4daf3e2750c49421e4c0cb193d7eb8">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>set_sensor_mode()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ad922bb51f10b3e181b1fa5252d48f443">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#afbc67a59155d3f0b4e69460e6b8aacc2">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a330df328fb76fd7901e76311131a3863">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>set_sensor_mode_idle()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a7dcebc52382cfdb72866673257e283fb">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>set_sensor_mode_run()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a79b4fa9389e01136ba6134b80059000a">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>set_session_id()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a3748f23da0a1e6582e7ac54ec11c400d">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>
</li>
<li>set_tx_center_frequency()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ac41c28eec9e1eda1b7313087cb69a3c0">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a9dc109f85098eded5ee441bc72e75329">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a30800f99e044d7f87c688706254b1cae">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>set_use_shadow()
: <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_block.xhtml#a54be27782c7eeed8020add4f7225ff58">pymoduleconnector.extras.regmap.RegBlock</a>
</li>
<li>set_user_header()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#ad1e010d865745ded5018f4dc3b70c6cf">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>
</li>
<li>start_bootloader()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#afc6e6c7f167e36affd9c58da070cbbd8">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a4e1c23e9cd07d718f61db5c3fc74d3bb">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ae9416a67bca04b001bda671cc194fd58">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ae4f26481fe14f8b2d18664d87a4b84b3">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>stop()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_player.xhtml#a2802828e18d240b71d957576e9c68d4e">pymoduleconnector.moduleconnectorwrapper.PyDataPlayer</a>
</li>
<li>stop_recording()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_recorder.xhtml#a9ac0e0a0a0a2d0d80d4fee6242f66ba6">pymoduleconnector.moduleconnectorwrapper.PyDataRecorder</a>
</li>
<li>store_noisemap()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a6bfd2b408a6887039a5024f0f0134b8f">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a0209fabce1f444169d3ebb57c1348e3a">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a05f0c89662aa753db403fb36f7587a4f">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>subscribe_to_baseband_ap()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a00d077c725bdc7bb2195c80f33d71066">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>subscribe_to_baseband_iq()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a894901e71ab517f72c2368f1adc0ad6a">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>subscribe_to_resp_status()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a7e9f4604aff87939e0a0a500bd6a4fee">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>subscribe_to_sleep_status()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#aacf18c7a963144fc5cd0c12f9e1e0a15">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>system_run_test()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ad9efe674f8049ed7969eeab892e273c9">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a473d2273063ea97d15e3c6b25d67968b">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a142cc1bff05a309bca9188a647a5cbe7">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
