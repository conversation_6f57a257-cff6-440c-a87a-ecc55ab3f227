<map id="Graphical Class Hierarchy" name="Graphical Class Hierarchy">
<area shape="rect" id="node1" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_chip_id_dig.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.ChipIdDig" alt="" coords="5,9628,199,9669"/>
<area shape="rect" id="node2" href="$classpymoduleconnector_1_1extras_1_1regmap_1_1_reg.xhtml" title="Generic register class. " alt="" coords="247,4822,497,4849"/>
<area shape="rect" id="node147" href="$classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_block.xhtml" title="The RegBlock class encapsulates one or more RegMap objects (register maps). " alt="" coords="259,9628,485,9669"/>
<area shape="rect" id="node149" href="$classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_map.xhtml" title="pymoduleconnector.extras.regmap.\lRegMap" alt="" coords="259,9693,485,9735"/>
<area shape="rect" id="node153" href="$classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_segment.xhtml" title="Register segment class. " alt="" coords="259,9759,485,9800"/>
<area shape="rect" id="node3" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_apc_dvdd_testmode.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.ApcDvddTestmode" alt="" coords="565,5,806,47"/>
<area shape="rect" id="node4" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_apc_temp_trim.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.ApcTempTrim" alt="" coords="580,71,791,112"/>
<area shape="rect" id="node5" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_avdd_rx_ctrl.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.AvddRxCtrl" alt="" coords="587,136,784,177"/>
<area shape="rect" id="node6" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_avdd_testmode.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.AvddTestmode" alt="" coords="577,201,794,243"/>
<area shape="rect" id="node7" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_avdd_tx_ctrl.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.AvddTxCtrl" alt="" coords="588,267,783,308"/>
<area shape="rect" id="node8" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_boot_from_otp_pif.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.BootFromOtpPif" alt="" coords="574,332,797,373"/>
<area shape="rect" id="node9" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_boot_from_otp_spi.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.BootFromOtpSpi" alt="" coords="572,397,799,439"/>
<area shape="rect" id="node10" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_chip_id_sys.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.ChipIdSys" alt="" coords="589,463,782,504"/>
<area shape="rect" id="node11" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_clkout_sel.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.ClkoutSel" alt="" coords="589,528,782,569"/>
<area shape="rect" id="node12" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_ctrl1.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.CommonPllCtrl1" alt="" coords="573,593,798,635"/>
<area shape="rect" id="node13" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_ctrl2.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.CommonPllCtrl2" alt="" coords="573,659,798,700"/>
<area shape="rect" id="node14" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_ctrl3.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.CommonPllCtrl3" alt="" coords="573,724,798,765"/>
<area shape="rect" id="node15" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_ctrl4.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.CommonPllCtrl4" alt="" coords="573,789,798,831"/>
<area shape="rect" id="node16" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_frac0.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.CommonPllFrac0" alt="" coords="570,855,801,896"/>
<area shape="rect" id="node17" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_frac1.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.CommonPllFrac1" alt="" coords="570,920,801,961"/>
<area shape="rect" id="node18" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_common_pll_frac2.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.CommonPllFrac2" alt="" coords="570,985,801,1027"/>
<area shape="rect" id="node19" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_cpu_reset.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.CpuReset" alt="" coords="589,1051,782,1092"/>
<area shape="rect" id="node20" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_cpu_spi_master_clk_ctrl.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.CpuSpiMasterClkCtrl" alt="" coords="559,1116,812,1157"/>
<area shape="rect" id="node21" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dac_anatestreq.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.DacAnatestreq" alt="" coords="577,1181,793,1223"/>
<area shape="rect" id="node22" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dac_trim.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.DacTrim" alt="" coords="589,1247,782,1288"/>
<area shape="rect" id="node23" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_debug.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.Debug" alt="" coords="589,1312,782,1353"/>
<area shape="rect" id="node24" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_debug_xif.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.DebugXif" alt="" coords="589,1377,782,1419"/>
<area shape="rect" id="node25" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dvdd_rx_ctrl.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.DvddRxCtrl" alt="" coords="586,1443,785,1484"/>
<area shape="rect" id="node26" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dvdd_testmode.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.DvddTestmode" alt="" coords="576,1508,795,1549"/>
<area shape="rect" id="node27" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_dvdd_tx_ctrl.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.DvddTxCtrl" alt="" coords="587,1573,783,1615"/>
<area shape="rect" id="node28" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_fetch_radar_data_pif.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.FetchRadarDataPif" alt="" coords="565,1639,806,1680"/>
<area shape="rect" id="node29" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_fetch_radar_data_spi.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.FetchRadarDataSpi" alt="" coords="563,1704,807,1745"/>
<area shape="rect" id="node30" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_firmware_version.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.FirmwareVersion" alt="" coords="572,1769,799,1811"/>
<area shape="rect" id="node31" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_firmware_version_spi.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.FirmwareVersionSpi" alt="" coords="562,1835,809,1876"/>
<area shape="rect" id="node32" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_force_one.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.ForceOne" alt="" coords="589,1900,782,1941"/>
<area shape="rect" id="node33" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_force_zero.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.ForceZero" alt="" coords="589,1965,782,2007"/>
<area shape="rect" id="node34" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_from_cpu_read_data.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.FromCpuReadData" alt="" coords="564,2031,807,2072"/>
<area shape="rect" id="node35" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_from_cpu_write_data.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.FromCpuWriteData" alt="" coords="564,2096,807,2137"/>
<area shape="rect" id="node36" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_from_mem_read_data.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.FromMemReadData" alt="" coords="562,2161,809,2203"/>
<area shape="rect" id="node37" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_gpio_in.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.GpioIn" alt="" coords="589,2227,782,2268"/>
<area shape="rect" id="node38" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_gpio_oe.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.GpioOe" alt="" coords="589,2292,782,2333"/>
<area shape="rect" id="node39" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_gpio_out.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.GpioOut" alt="" coords="589,2357,782,2399"/>
<area shape="rect" id="node40" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl1.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.IoCtrl1" alt="" coords="589,2423,782,2464"/>
<area shape="rect" id="node41" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl2.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.IoCtrl2" alt="" coords="589,2488,782,2529"/>
<area shape="rect" id="node42" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl3.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.IoCtrl3" alt="" coords="589,2553,782,2595"/>
<area shape="rect" id="node43" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl4.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.IoCtrl4" alt="" coords="589,2619,782,2660"/>
<area shape="rect" id="node44" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl5.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.IoCtrl5" alt="" coords="589,2684,782,2725"/>
<area shape="rect" id="node45" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_io_ctrl6.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.IoCtrl6" alt="" coords="589,2749,782,2791"/>
<area shape="rect" id="node46" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_iref_trim.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.IrefTrim" alt="" coords="589,2815,782,2856"/>
<area shape="rect" id="node47" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_ldo_status1.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.LdoStatus1" alt="" coords="588,2880,783,2921"/>
<area shape="rect" id="node48" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_ldo_status2.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.LdoStatus2" alt="" coords="588,2945,783,2987"/>
<area shape="rect" id="node49" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_lna_anatestreq.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.LnaAnatestreq" alt="" coords="579,3011,792,3052"/>
<area shape="rect" id="node50" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_lock_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.LockStatus" alt="" coords="588,3076,783,3117"/>
<area shape="rect" id="node51" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mclk_trx_backend_clk_ctrl.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.MclkTrxBackendClkCtrl" alt="" coords="551,3141,819,3183"/>
<area shape="rect" id="node52" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mcu_bist_ctrl.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.McuBistCtrl" alt="" coords="586,3207,785,3248"/>
<area shape="rect" id="node53" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mcu_bist_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.McuBistStatus" alt="" coords="578,3272,793,3313"/>
<area shape="rect" id="node54" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mem_first_addr_lsb.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.MemFirstAddrLsb" alt="" coords="569,3337,801,3379"/>
<area shape="rect" id="node55" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mem_first_addr_msb.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.MemFirstAddrMsb" alt="" coords="567,3403,803,3444"/>
<area shape="rect" id="node56" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_mem_mode.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.MemMode" alt="" coords="589,3468,782,3509"/>
<area shape="rect" id="node57" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_misc_ctrl.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.MiscCtrl" alt="" coords="589,3533,782,3575"/>
<area shape="rect" id="node58" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_osc_ctrl.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.OscCtrl" alt="" coords="589,3599,782,3640"/>
<area shape="rect" id="node59" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_otp_ctrl.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.OtpCtrl" alt="" coords="589,3664,782,3705"/>
<area shape="rect" id="node60" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_mb_clear_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.PifMbClearStatus" alt="" coords="569,3729,801,3771"/>
<area shape="rect" id="node61" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_mb_fifo_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.PifMbFifoStatus" alt="" coords="574,3795,797,3836"/>
<area shape="rect" id="node62" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_mem_clear_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.PifMemClearStatus" alt="" coords="564,3860,807,3901"/>
<area shape="rect" id="node63" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_mem_fifo_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.PifMemFifoStatus" alt="" coords="568,3925,803,3967"/>
<area shape="rect" id="node64" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data0_clear_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.PifRadarData0Clear\lStatus" alt="" coords="562,3990,809,4046"/>
<area shape="rect" id="node65" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data0_fifo_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.PifRadarData0Fifo\lStatus" alt="" coords="567,4070,804,4126"/>
<area shape="rect" id="node66" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data1_clear_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.PifRadarData1Clear\lStatus" alt="" coords="562,4150,809,4206"/>
<area shape="rect" id="node67" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data1_fifo_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.PifRadarData1Fifo\lStatus" alt="" coords="567,4230,804,4286"/>
<area shape="rect" id="node68" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_pif_radar_data_clear_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.PifRadarDataClear\lStatus" alt="" coords="566,4310,805,4366"/>
<area shape="rect" id="node69" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_preamp_trim.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.PreampTrim" alt="" coords="585,4389,785,4431"/>
<area shape="rect" id="node70" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_bist_ctrl.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RadarBistCtrl" alt="" coords="581,4455,789,4496"/>
<area shape="rect" id="node71" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_bist_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RadarBistStatus" alt="" coords="573,4520,798,4561"/>
<area shape="rect" id="node72" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_data_pif.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RadarDataPif" alt="" coords="581,4585,789,4627"/>
<area shape="rect" id="node73" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_data_pif_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RadarDataPifStatus" alt="" coords="562,4651,809,4692"/>
<area shape="rect" id="node74" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_data_spi.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RadarDataSpi" alt="" coords="580,4716,791,4757"/>
<area shape="rect" id="node75" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_data_spi_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RadarDataSpiStatus" alt="" coords="561,4781,810,4823"/>
<area shape="rect" id="node76" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_radar_readout_idle.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RadarReadoutIdle" alt="" coords="568,4847,803,4888"/>
<area shape="rect" id="node77" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_ram_select.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RamSelect" alt="" coords="588,4912,783,4953"/>
<area shape="rect" id="node78" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_counter_lsb.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxCounterLsb" alt="" coords="579,4977,792,5019"/>
<area shape="rect" id="node79" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_counter_num_bytes.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxCounterNumBytes" alt="" coords="558,5043,813,5084"/>
<area shape="rect" id="node80" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_downconversion_coeff_i1.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxDownconversion\lCoeffI1" alt="" coords="564,5107,807,5163"/>
<area shape="rect" id="node81" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_downconversion_coeff_i2.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxDownconversion\lCoeffI2" alt="" coords="564,5187,807,5243"/>
<area shape="rect" id="node82" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_downconversion_coeff_q1.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxDownconversion\lCoeffQ1" alt="" coords="564,5267,807,5323"/>
<area shape="rect" id="node83" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_downconversion_coeff_q2.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxDownconversion\lCoeffQ2" alt="" coords="564,5347,807,5403"/>
<area shape="rect" id="node84" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_fe_anatestreq.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxFeAnatestreq" alt="" coords="573,5427,797,5468"/>
<area shape="rect" id="node85" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_mframes.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxMframes" alt="" coords="587,5492,784,5533"/>
<area shape="rect" id="node86" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_mframes_coarse.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxMframesCoarse" alt="" coords="566,5557,805,5599"/>
<area shape="rect" id="node87" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_ctrl1.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxPllCtrl1" alt="" coords="589,5623,782,5664"/>
<area shape="rect" id="node88" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_ctrl2.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxPllCtrl2" alt="" coords="589,5688,782,5729"/>
<area shape="rect" id="node89" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_skewcalin.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxPllSkewcalin" alt="" coords="575,5753,796,5795"/>
<area shape="rect" id="node90" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_skew_ctrl.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxPllSkewCtrl" alt="" coords="578,5819,793,5860"/>
<area shape="rect" id="node91" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_pll_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxPllStatus" alt="" coords="586,5884,785,5925"/>
<area shape="rect" id="node92" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_ram_line_first_msb.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxRamLineFirstMsb" alt="" coords="561,5949,810,5991"/>
<area shape="rect" id="node93" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_ram_line_last_msb.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxRamLineLastMsb" alt="" coords="561,6015,809,6056"/>
<area shape="rect" id="node94" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_ram_lsbs.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxRamLsbs" alt="" coords="584,6080,787,6121"/>
<area shape="rect" id="node95" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_ram_write_offset_msb.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxRamWriteOffsetMsb" alt="" coords="552,6145,819,6187"/>
<area shape="rect" id="node96" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_reset_counters.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxResetCounters" alt="" coords="568,6211,803,6252"/>
<area shape="rect" id="node97" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_rx_wait.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.RxWait" alt="" coords="589,6276,782,6317"/>
<area shape="rect" id="node98" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_sampler_preset_lsb.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SamplerPresetLsb" alt="" coords="567,6341,803,6383"/>
<area shape="rect" id="node99" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_sampler_preset_msb.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SamplerPresetMsb" alt="" coords="565,6407,805,6448"/>
<area shape="rect" id="node100" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_smpl_mode.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SmplMode" alt="" coords="589,6472,782,6513"/>
<area shape="rect" id="node101" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_config.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiConfig" alt="" coords="589,6537,782,6579"/>
<area shape="rect" id="node102" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_config_pif.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiConfigPif" alt="" coords="584,6603,787,6644"/>
<area shape="rect" id="node103" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_idle.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMasterIdle" alt="" coords="581,6668,790,6709"/>
<area shape="rect" id="node104" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_mode.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMasterMode" alt="" coords="575,6733,795,6775"/>
<area shape="rect" id="node105" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_radar_burst_kick.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMasterRadarBurstKick" alt="" coords="545,6799,825,6840"/>
<area shape="rect" id="node106" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_radar_burst_size_lsb.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMasterRadarBurst\lSizeLsb" alt="" coords="558,6863,813,6919"/>
<area shape="rect" id="node107" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_master_send.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMasterSend" alt="" coords="576,6943,795,6984"/>
<area shape="rect" id="node108" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mb_clear_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMbClearStatus" alt="" coords="568,7008,803,7049"/>
<area shape="rect" id="node109" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mb_fifo_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMbFifoStatus" alt="" coords="572,7073,799,7115"/>
<area shape="rect" id="node110" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mem_clear_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMemClearStatus" alt="" coords="562,7139,809,7180"/>
<area shape="rect" id="node111" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_mem_fifo_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiMemFifoStatus" alt="" coords="567,7204,804,7245"/>
<area shape="rect" id="node112" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data0_clear_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiRadarData0Clear\lStatus" alt="" coords="561,7269,810,7325"/>
<area shape="rect" id="node113" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data0_fifo_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiRadarData0Fifo\lStatus" alt="" coords="565,7349,805,7405"/>
<area shape="rect" id="node114" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data1_clear_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiRadarData1Clear\lStatus" alt="" coords="561,7429,810,7485"/>
<area shape="rect" id="node115" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data1_fifo_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiRadarData1Fifo\lStatus" alt="" coords="565,7509,805,7565"/>
<area shape="rect" id="node116" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_spi_radar_data_clear_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SpiRadarDataClear\lStatus" alt="" coords="564,7589,807,7645"/>
<area shape="rect" id="node117" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_to_cpu_read_data.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.ToCpuReadData" alt="" coords="572,7668,799,7709"/>
<area shape="rect" id="node118" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_to_cpu_write_data.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.ToCpuWriteData" alt="" coords="572,7733,799,7775"/>
<area shape="rect" id="node119" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_to_mem_write_data.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.ToMemWriteData" alt="" coords="569,7799,801,7840"/>
<area shape="rect" id="node120" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_backend_done.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxBackendDone" alt="" coords="570,7864,801,7905"/>
<area shape="rect" id="node121" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_clocks_per_pulse.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxClocksPerPulse" alt="" coords="564,7929,807,7971"/>
<area shape="rect" id="node122" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_ctrl_done.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxCtrlDone" alt="" coords="585,7995,785,8036"/>
<area shape="rect" id="node123" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_ctrl_mode.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxCtrlMode" alt="" coords="585,8060,786,8101"/>
<area shape="rect" id="node124" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_max_h.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacMaxH" alt="" coords="582,8125,789,8167"/>
<area shape="rect" id="node125" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_max_l.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacMaxL" alt="" coords="583,8191,787,8232"/>
<area shape="rect" id="node126" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_min_h.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacMinH" alt="" coords="584,8256,787,8297"/>
<area shape="rect" id="node127" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_min_l.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacMinL" alt="" coords="585,8321,785,8363"/>
<area shape="rect" id="node128" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_override_h.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacOverrideH" alt="" coords="570,8387,801,8428"/>
<area shape="rect" id="node129" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_override_l.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacOverrideL" alt="" coords="571,8452,799,8493"/>
<area shape="rect" id="node130" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_override_load.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacOverrideLoad" alt="" coords="561,8517,810,8559"/>
<area shape="rect" id="node131" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_step.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxDacStep" alt="" coords="586,8583,785,8624"/>
<area shape="rect" id="node132" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_iterations.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxIterations" alt="" coords="584,8648,787,8689"/>
<area shape="rect" id="node133" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_lfsr_reset.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxLfsrReset" alt="" coords="583,8713,788,8755"/>
<area shape="rect" id="node134" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_lfsr_taps0.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxLfsrTaps0" alt="" coords="582,8779,789,8820"/>
<area shape="rect" id="node135" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_lfsr_taps1.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxLfsrTaps1" alt="" coords="582,8844,789,8885"/>
<area shape="rect" id="node136" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_lfsr_taps2.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxLfsrTaps2" alt="" coords="582,8909,789,8951"/>
<area shape="rect" id="node137" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_pulses_per_step_lsb.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxPulsesPerStepLsb" alt="" coords="557,8975,813,9016"/>
<area shape="rect" id="node138" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_pulses_per_step_msb.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxPulsesPerStepMsb" alt="" coords="555,9040,815,9081"/>
<area shape="rect" id="node139" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_start.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TrxStart" alt="" coords="589,9105,782,9147"/>
<area shape="rect" id="node140" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_ctrl1.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TxPllCtrl1" alt="" coords="589,9171,782,9212"/>
<area shape="rect" id="node141" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_ctrl2.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TxPllCtrl2" alt="" coords="589,9236,782,9277"/>
<area shape="rect" id="node142" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_skewcalin.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TxPllSkewcalin" alt="" coords="576,9301,795,9343"/>
<area shape="rect" id="node143" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_skew_ctrl.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TxPllSkewCtrl" alt="" coords="579,9367,792,9408"/>
<area shape="rect" id="node144" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_pll_status.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TxPllStatus" alt="" coords="587,9432,784,9473"/>
<area shape="rect" id="node145" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_tx_wait.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.TxWait" alt="" coords="589,9497,782,9539"/>
<area shape="rect" id="node146" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_vref_trim.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.VrefTrim" alt="" coords="589,9563,782,9604"/>
<area shape="rect" id="node148" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_x4.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.X4" alt="" coords="589,9628,782,9669"/>
<area shape="rect" id="node150" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_p_i_f.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.PIF" alt="" coords="589,9693,782,9735"/>
<area shape="rect" id="node151" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_s_p_i.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.SPI" alt="" coords="589,9759,782,9800"/>
<area shape="rect" id="node152" href="$classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_x_i_f.xhtml" title="pymoduleconnector.extras.x4\l_regmap_autogen.XIF" alt="" coords="589,9824,782,9865"/>
</map>
