<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.38.0 (20140413.2041)
 -->
<!-- Title: Graphical Class Hierarchy Pages: 1 -->
<svg width="315pt" height="39pt"
 viewBox="0.00 0.00 315.00 39.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 35)">
<title>Graphical Class Hierarchy</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-35 311,-35 311,4 -4,4"/>
<!-- Node2 -->
<g id="node1" class="node"><title>Node2</title>
<polygon fill="white" stroke="#bfbfbf" points="0,-6 0,-25 89,-25 89,-6 0,-6"/>
<text text-anchor="middle" x="44.5" y="-13" font-family="Helvetica,sans-Serif" font-size="10.00">PyDataRecorder</text>
</g>
<!-- Node0 -->
<g id="node2" class="node"><title>Node0</title>
<g id="a_node2"><a xlink:href="classpymoduleconnector_1_1moduleconnector_1_1_data_recorder.xhtml" target="_top" xlink:title="Inherits pymoduleconnector.moduleconnectorwrapper.PyDataRecorder. ">
<polygon fill="white" stroke="black" points="125,-0.5 125,-30.5 307,-30.5 307,-0.5 125,-0.5"/>
<text text-anchor="start" x="133" y="-18.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.moduleconnector.</text>
<text text-anchor="middle" x="216" y="-7.5" font-family="Helvetica,sans-Serif" font-size="10.00">DataRecorder</text>
</a>
</g>
</g>
<!-- Node2&#45;&gt;Node0 -->
<g id="edge1" class="edge"><title>Node2&#45;&gt;Node0</title>
<path fill="none" stroke="midnightblue" d="M99.2286,-15.5C107.395,-15.5 116.007,-15.5 124.698,-15.5"/>
<polygon fill="midnightblue" stroke="midnightblue" points="99.1505,-12.0001 89.1505,-15.5 99.1504,-19.0001 99.1505,-12.0001"/>
</g>
</g>
</svg>
