<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.extras.auto Namespace Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>extras</b></li><li class="navelem"><a class="el" href="namespacepymoduleconnector_1_1extras_1_1auto.xhtml">auto</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.extras.auto Namespace Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Automatically locating modules for ModuleConnector.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a4575b35abcc35183f3e7e569b93cad5d"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacepymoduleconnector_1_1extras_1_1auto.xhtml#a4575b35abcc35183f3e7e569b93cad5d">auto</a> (dev='all')</td></tr>
<tr class="memdesc:a4575b35abcc35183f3e7e569b93cad5d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Automatically find modules.  <a href="#a4575b35abcc35183f3e7e569b93cad5d">More...</a><br /></td></tr>
<tr class="separator:a4575b35abcc35183f3e7e569b93cad5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac3a72f65df1021625aca1f36c7de0b9"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacepymoduleconnector_1_1extras_1_1auto.xhtml#aac3a72f65df1021625aca1f36c7de0b9">auto_open</a> (dev='all', ix=0, log_level=0)</td></tr>
<tr class="memdesc:aac3a72f65df1021625aca1f36c7de0b9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Open an automatically found module.  <a href="#aac3a72f65df1021625aca1f36c7de0b9">More...</a><br /></td></tr>
<tr class="separator:aac3a72f65df1021625aca1f36c7de0b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1b37b08fc326ca5067067e114c71f23a"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacepymoduleconnector_1_1extras_1_1auto.xhtml#a1b37b08fc326ca5067067e114c71f23a">create_auto</a> (args, kwargs)</td></tr>
<tr class="memdesc:a1b37b08fc326ca5067067e114c71f23a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create a context managed ModuleConnector instance.  <a href="#a1b37b08fc326ca5067067e114c71f23a">More...</a><br /></td></tr>
<tr class="separator:a1b37b08fc326ca5067067e114c71f23a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Automatically locating modules for ModuleConnector. </p>
</div><h2 class="groupheader">Function Documentation</h2>
<a id="a4575b35abcc35183f3e7e569b93cad5d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4575b35abcc35183f3e7e569b93cad5d">&sect;&nbsp;</a></span>auto()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.extras.auto.auto </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>dev</em> = <code>'all'</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Automatically find modules. </p>
<h2>Arguments </h2>
<p><code>dev</code> : a comma-separated list of modules to search for, and may contain ftdi, bl, bootloader, x4, and all.</p>
<p>Returns a list of matching devices. </p>

</div>
</div>
<a id="aac3a72f65df1021625aca1f36c7de0b9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aac3a72f65df1021625aca1f36c7de0b9">&sect;&nbsp;</a></span>auto_open()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.extras.auto.auto_open </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>dev</em> = <code>'all'</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>ix</em> = <code>0</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>log_level</em> = <code>0</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Open an automatically found module. </p>
<h2>Arguments </h2>
<p>dev string Same as for <code>auto</code> ix int index into the list returned by <a class="el" href="namespacepymoduleconnector_1_1extras_1_1auto.xhtml#a4575b35abcc35183f3e7e569b93cad5d" title="Automatically find modules. ">auto()</a> to open. log_level int argument to <code>ModuleConnector</code> for how much information to print</p>
<h2>Throws </h2>
<p><code>IndexError</code> : if the module was not found</p>
<h2>Returns </h2>
<p><code><a class="el" href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml" title="Inherits pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector. ">pymoduleconnector.moduleconnector.ModuleConnector</a></code> instance </p>

</div>
</div>
<a id="a1b37b08fc326ca5067067e114c71f23a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1b37b08fc326ca5067067e114c71f23a">&sect;&nbsp;</a></span>create_auto()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.extras.auto.create_auto </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>args</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>kwargs</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Create a context managed ModuleConnector instance. </p>
<p><a class="el" href="namespacepymoduleconnector_1_1moduleconnector.xhtml#ad56b7daa5d0bcd52f411b5bbb7e77e68" title="Initiate a context managed ModuleConnector object. ">pymoduleconnector.moduleconnector.create_mc</a>, but for auto. </p>

</div>
</div>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
