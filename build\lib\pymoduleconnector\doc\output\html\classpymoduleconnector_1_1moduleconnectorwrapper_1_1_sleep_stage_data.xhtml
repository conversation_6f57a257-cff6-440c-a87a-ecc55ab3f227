<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnectorwrapper.SleepStageData Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_stage_data.xhtml">SleepStageData</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_stage_data-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.SleepStageData Class Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ad00cdb2f45ed92236cfcffc03494ac17"><td class="memItemLeft" align="right" valign="top"><a id="ad00cdb2f45ed92236cfcffc03494ac17"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_stage_data.xhtml#ad00cdb2f45ed92236cfcffc03494ac17">__init__</a> (self)</td></tr>
<tr class="memdesc:ad00cdb2f45ed92236cfcffc03494ac17"><td class="mdescLeft">&#160;</td><td class="mdescRight"><b>init</b>(XeThru::SleepStageData self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_stage_data.xhtml">SleepStageData</a> <br /></td></tr>
<tr class="separator:ad00cdb2f45ed92236cfcffc03494ac17"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a9db29944bb81a340003556c63a9bcc63"><td class="memItemLeft" align="right" valign="top"><a id="a9db29944bb81a340003556c63a9bcc63"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>this</b></td></tr>
<tr class="separator:a9db29944bb81a340003556c63a9bcc63"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a2e2fa4bf339da8062b04f42358d3c529"><td class="memItemLeft" align="right" valign="top"><a id="a2e2fa4bf339da8062b04f42358d3c529"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>frame_counter</b> = _swig_property(_moduleconnectorwrapper.SleepStageData_frame_counter_get, _moduleconnectorwrapper.SleepStageData_frame_counter_set)</td></tr>
<tr class="separator:a2e2fa4bf339da8062b04f42358d3c529"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3e7b481e92b394d6df8cddb9bccf70be"><td class="memItemLeft" align="right" valign="top"><a id="a3e7b481e92b394d6df8cddb9bccf70be"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>sleepstage</b> = _swig_property(_moduleconnectorwrapper.SleepStageData_sleepstage_get, _moduleconnectorwrapper.SleepStageData_sleepstage_set)</td></tr>
<tr class="separator:a3e7b481e92b394d6df8cddb9bccf70be"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5667f9eef72c82497d171948b9a3e798"><td class="memItemLeft" align="right" valign="top"><a id="a5667f9eef72c82497d171948b9a3e798"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>confidence</b> = _swig_property(_moduleconnectorwrapper.SleepStageData_confidence_get, _moduleconnectorwrapper.SleepStageData_confidence_set)</td></tr>
<tr class="separator:a5667f9eef72c82497d171948b9a3e798"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/moduleconnectorwrapper/__init__.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
