<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.38.0 (20140413.2041)
 -->
<!-- Title: Graph Legend Pages: 1 -->
<svg width="512pt" height="159pt"
 viewBox="0.00 0.00 512.00 159.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 155)">
<title>Graph Legend</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-155 508,-155 508,4 -4,4"/>
<!-- Node9 -->
<g id="node1" class="node"><title>Node9</title>
<polygon fill="#bfbfbf" stroke="black" points="293,-19 239,-19 239,-0 293,-0 293,-19"/>
<text text-anchor="middle" x="266" y="-7" font-family="Helvetica,sans-Serif" font-size="10.00">Inherited</text>
</g>
<!-- Node10 -->
<g id="node2" class="node"><title>Node10</title>
<g id="a_node2"><a xlink:href="$classPublicBase.xhtml" xlink:title="PublicBase">
<polygon fill="none" stroke="black" points="66,-85 0,-85 0,-66 66,-66 66,-85"/>
<text text-anchor="middle" x="33" y="-73" font-family="Helvetica,sans-Serif" font-size="10.00">PublicBase</text>
</a>
</g>
</g>
<!-- Node10&#45;&gt;Node9 -->
<g id="edge1" class="edge"><title>Node10&#45;&gt;Node9</title>
<path fill="none" stroke="midnightblue" d="M74.07,-63.219C121.464,-50.2008 198.076,-29.1573 238.691,-18.0013"/>
<polygon fill="midnightblue" stroke="midnightblue" points="73.1392,-59.8449 64.4234,-65.8687 74.9933,-66.5949 73.1392,-59.8449"/>
</g>
<!-- Node11 -->
<g id="node3" class="node"><title>Node11</title>
<g id="a_node3"><a xlink:href="$classTruncated.xhtml" xlink:title="Truncated">
<polygon fill="none" stroke="red" points="63,-151 3,-151 3,-132 63,-132 63,-151"/>
<text text-anchor="middle" x="33" y="-139" font-family="Helvetica,sans-Serif" font-size="10.00">Truncated</text>
</a>
</g>
</g>
<!-- Node11&#45;&gt;Node10 -->
<g id="edge2" class="edge"><title>Node11&#45;&gt;Node10</title>
<path fill="none" stroke="midnightblue" d="M33,-121.584C33,-109.625 33,-94.7211 33,-85.4052"/>
<polygon fill="midnightblue" stroke="midnightblue" points="29.5001,-121.869 33,-131.869 36.5001,-121.869 29.5001,-121.869"/>
</g>
<!-- Node13 -->
<g id="node4" class="node"><title>Node13</title>
<g id="a_node4"><a xlink:href="$classProtectedBase.xhtml" xlink:title="ProtectedBase">
<polygon fill="none" stroke="black" points="165.5,-85 84.5,-85 84.5,-66 165.5,-66 165.5,-85"/>
<text text-anchor="middle" x="125" y="-73" font-family="Helvetica,sans-Serif" font-size="10.00">ProtectedBase</text>
</a>
</g>
</g>
<!-- Node13&#45;&gt;Node9 -->
<g id="edge3" class="edge"><title>Node13&#45;&gt;Node9</title>
<path fill="none" stroke="darkgreen" d="M153.213,-61.6939C181.001,-49.081 222.69,-30.1584 246.835,-19.199"/>
<polygon fill="darkgreen" stroke="darkgreen" points="151.675,-58.5484 144.016,-65.8687 154.568,-64.9225 151.675,-58.5484"/>
</g>
<!-- Node14 -->
<g id="node5" class="node"><title>Node14</title>
<g id="a_node5"><a xlink:href="$classPrivateBase.xhtml" xlink:title="PrivateBase">
<polygon fill="none" stroke="black" points="254,-85 184,-85 184,-66 254,-66 254,-85"/>
<text text-anchor="middle" x="219" y="-73" font-family="Helvetica,sans-Serif" font-size="10.00">PrivateBase</text>
</a>
</g>
</g>
<!-- Node14&#45;&gt;Node9 -->
<g id="edge4" class="edge"><title>Node14&#45;&gt;Node9</title>
<path fill="none" stroke="#8b1a1a" d="M231.288,-57.7673C240.304,-45.4902 252.196,-29.2973 259.46,-19.4052"/>
<polygon fill="#8b1a1a" stroke="#8b1a1a" points="228.437,-55.7369 225.339,-65.8687 234.079,-59.8803 228.437,-55.7369"/>
</g>
<!-- Node15 -->
<g id="node6" class="node"><title>Node15</title>
<polygon fill="none" stroke="#bfbfbf" points="355.5,-85 272.5,-85 272.5,-66 355.5,-66 355.5,-85"/>
<text text-anchor="middle" x="314" y="-73" font-family="Helvetica,sans-Serif" font-size="10.00">Undocumented</text>
</g>
<!-- Node15&#45;&gt;Node9 -->
<g id="edge5" class="edge"><title>Node15&#45;&gt;Node9</title>
<path fill="none" stroke="midnightblue" d="M301.45,-57.7673C292.243,-45.4902 280.098,-29.2973 272.679,-19.4052"/>
<polygon fill="midnightblue" stroke="midnightblue" points="298.726,-59.9687 307.527,-65.8687 304.326,-55.7686 298.726,-59.9687"/>
</g>
<!-- Node16 -->
<g id="node7" class="node"><title>Node16</title>
<g id="a_node7"><a xlink:href="$classTempl.xhtml" xlink:title="Templ&lt; int &gt;">
<polygon fill="none" stroke="black" points="446,-85 374,-85 374,-66 446,-66 446,-85"/>
<text text-anchor="middle" x="410" y="-73" font-family="Helvetica,sans-Serif" font-size="10.00">Templ&lt; int &gt;</text>
</a>
</g>
</g>
<!-- Node16&#45;&gt;Node9 -->
<g id="edge6" class="edge"><title>Node16&#45;&gt;Node9</title>
<path fill="none" stroke="midnightblue" d="M381.186,-61.6939C352.807,-49.081 310.231,-30.1584 285.573,-19.199"/>
<polygon fill="midnightblue" stroke="midnightblue" points="380.02,-65.0056 390.58,-65.8687 382.863,-58.6089 380.02,-65.0056"/>
</g>
<!-- Node17 -->
<g id="node8" class="node"><title>Node17</title>
<g id="a_node8"><a xlink:href="$classTempl.xhtml" xlink:title="Templ&lt; T &gt;">
<polygon fill="none" stroke="black" points="444,-151 376,-151 376,-132 444,-132 444,-151"/>
<text text-anchor="middle" x="410" y="-139" font-family="Helvetica,sans-Serif" font-size="10.00">Templ&lt; T &gt;</text>
</a>
</g>
</g>
<!-- Node17&#45;&gt;Node16 -->
<g id="edge7" class="edge"><title>Node17&#45;&gt;Node16</title>
<path fill="none" stroke="orange" stroke-dasharray="5,2" d="M410,-121.584C410,-109.625 410,-94.7211 410,-85.4052"/>
<polygon fill="orange" stroke="orange" points="406.5,-121.869 410,-131.869 413.5,-121.869 406.5,-121.869"/>
<text text-anchor="middle" x="424.5" y="-106" font-family="Helvetica,sans-Serif" font-size="10.00">&lt; int &gt;</text>
</g>
<!-- Node18 -->
<g id="node9" class="node"><title>Node18</title>
<g id="a_node9"><a xlink:href="$classUsed.xhtml" xlink:title="Used">
<polygon fill="none" stroke="black" points="504,-85 464,-85 464,-66 504,-66 504,-85"/>
<text text-anchor="middle" x="484" y="-73" font-family="Helvetica,sans-Serif" font-size="10.00">Used</text>
</a>
</g>
</g>
<!-- Node18&#45;&gt;Node9 -->
<g id="edge8" class="edge"><title>Node18&#45;&gt;Node9</title>
<path fill="none" stroke="#9a32cd" stroke-dasharray="5,2" d="M454.106,-65.7239C411.551,-53.2306 334.132,-30.502 293.142,-18.4682"/>
<polygon fill="#9a32cd" stroke="#9a32cd" points="453.301,-69.1351 463.882,-68.5938 455.273,-62.4185 453.301,-69.1351"/>
<text text-anchor="middle" x="419.5" y="-40" font-family="Helvetica,sans-Serif" font-size="10.00">m_usedClass</text>
</g>
</g>
</svg>
