<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_g"></a>- g -</h3><ul>
<li>get_amplitude()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_ap_data.xhtml#aab6d63d054ee16ae8b815b34dae6fb3e">pymoduleconnector.moduleconnectorwrapper.BasebandApData</a>
</li>
<li>get_app_id_list()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#ab863661b0d2c0a6ef0c5f69c31710808">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>get_baseband_ap_data()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a3279484711dbcf08cc1f6ec71dff869b">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>get_baseband_iq_data()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#aa2dd35d34f70660ae8c26fc0ee59bf42">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>get_basename_for_data_type()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_recorder.xhtml#ac4eec45ad4315791969456d0415a3c83">pymoduleconnector.moduleconnectorwrapper.PyDataRecorder</a>
</li>
<li>get_build_info()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a16616e599b8614986a288bd535d982de">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>get_byte_count()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml#a446b1354dfd6752be8ee76bf61dea175">pymoduleconnector.moduleconnectorwrapper.PreferredSplitSize</a>
</li>
<li>get_data()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_data_record.xhtml#a6864ea93c9cc36cfad83e23f56ec94cb">pymoduleconnector.moduleconnectorwrapper.DataRecord</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_byte_data.xhtml#a068faac02990de06d93107943cba7e49">pymoduleconnector.moduleconnectorwrapper.PulseDopplerByteData</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_float_data.xhtml#a271553d6e771192cf46240aec8743523">pymoduleconnector.moduleconnectorwrapper.PulseDopplerFloatData</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_data.xhtml#aafc6f95efeaa3b8ade825c9dda8f6642">pymoduleconnector.moduleconnectorwrapper.RadarRfData</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml#a542fb2c1ddede9a8cffeca3473af6eef">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a>
</li>
<li>get_data_rate_limit()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a20fa9eabb0ed9ec8147d44738848854f">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>
</li>
<li>get_data_recorder()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml#a7dec0e8d5ddb52ebbb072f9acd576ef1">pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector</a>
</li>
<li>get_data_types()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml#ab306acd00d6e7ad29ec0d21e6750134c">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a>
</li>
<li>get_debug_output_control()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a6cb2067b80882acd78845bdd03453a3a">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aac7e55dbb655536bdacaaf535eab7bd5">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a5b4a0068b83623251b89cdfefe508e08">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>get_decimation_factor()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a231ece70d21ccb2807a1f75e473d848b">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>get_detection_zone()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a3c4fd9c3c37e04d27cd3202c6a8f10e6">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a8b4773b7c852d0c4e2c5bac8ab128f83">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#aa9bb9b53f33af8a80d06d353893ca181">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>get_detection_zone_limits()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a60d73a9109e3b27d95024751a7ac954a">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a98ac4c7f8c7d54b3b31edd1abd3c2bda">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#acd9819f3280ad70c955acfdf0e04504a">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>get_directory_split_size()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a6217bf22e4808de72736154fc99c54fd">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>
</li>
<li>get_duration()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml#a3650d9e6a9a6ec1ed93256c16836ee46">pymoduleconnector.moduleconnectorwrapper.PreferredSplitSize</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_player.xhtml#a444184cbb27eae81fec3d43d22ec844f">pymoduleconnector.moduleconnectorwrapper.PyDataPlayer</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml#a22916a88891aafb8144df25f402b3fbc">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a>
</li>
<li>get_file()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1395e4e1dc44d68e3a14f2fb9521002c">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>get_file_data()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ab211390dea6d5c536169c759da0fad3e">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>get_file_identifier_items()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_files.xhtml#ad22c8ff4ef62f9133d996fdf94de11a9">pymoduleconnector.moduleconnectorwrapper.Files</a>
</li>
<li>get_file_length()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a43c8064e3969ab3a04312390c600df2e">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>get_file_split_size()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a36c3a4834bf15f78e57918e318086d4a">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>
</li>
<li>get_file_type_items()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_files.xhtml#a3e6917e391ecd517b2f1f418a0c7786f">pymoduleconnector.moduleconnectorwrapper.Files</a>
</li>
<li>get_filter()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_player.xhtml#af350926ed1ca74791be9c1dd063f10a5">pymoduleconnector.moduleconnectorwrapper.PyDataPlayer</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml#a705c594eea8fd18623b75337450c8628">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a>
</li>
<li>get_firmware_id()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a10afd1cd98a04bb203dfc3db50a9a173">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>get_firmware_version()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a810e5d07ede824e4d042cfc9523135f7">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>get_fixed_daily_hour()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml#a174f06b3429cb542bff5ecc4187db8cb">pymoduleconnector.moduleconnectorwrapper.PreferredSplitSize</a>
</li>
<li>get_flush_on_write()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a7df54e8220e70427c10478887935a096">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>
</li>
<li>get_I()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_iq_data.xhtml#a9149b48bc7b11a8b7acc72ea2a9b625d">pymoduleconnector.moduleconnectorwrapper.BasebandIqData</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_float_data.xhtml#ac7d8dab9a210580b6b6cd36ae2c10a1f">pymoduleconnector.moduleconnectorwrapper.RadarBasebandFloatData</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_q15_data.xhtml#ab429a717bcfc090b17c2aaf073d6fe17">pymoduleconnector.moduleconnectorwrapper.RadarBasebandQ15Data</a>
</li>
<li>get_iopin_control()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a2cf786b0a023f8c2ce986334029d9d51">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ab3af7caf671c18da3a01c552ee7429d0">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a8144aa7719f885017b82f9f7540fdd01">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>get_iopin_value()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a75cb41ee97156e5ec92ba453b71c5463">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a7ecc0325b94a6ed28d2179f6bca2237a">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a9b5e91df6369e644320d0a0ec2a2e109">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ae820fc6d6ae63288451fb850cb665854">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>get_item_number()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a465a516cea86e699e02b1723e3fdd842">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>get_led_control()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a589b2cafac9301bd9ec10836c875af34">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a48696644525888d29cbdc919efce04c4">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a222ce51e298e12567df5fd366cfab713">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>get_legacy_output()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a646697273e17f2cb85e5304fae4fc7a5">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>get_loop_mode_enabled()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_player.xhtml#a494005cf3782e1919cf09ffca8aa3f13">pymoduleconnector.moduleconnectorwrapper.PyDataPlayer</a>
</li>
<li>get_max_record_size()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml#ad9e73c279ae7aefa618bd2cca8c46edf">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a>
</li>
<li>get_noisemap_control()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a6e972f77ffe131a30f890275b465a5a8">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a0b63354cbae400123b81d1ec5c0fd1fe">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#add9b5c1f49d7a2683f63ba8ed73c9329">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>get_normalization()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a5d7817820c63f694707659d5c5fefb95">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>get_number_format()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a40ea1cb1f173ae5127f156191e69ec3b">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>get_number_of_packets()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a7e7a107912a579d2de8584fb7216bac6">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>get_order_code()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a9d4eaa480b55f97012019019696a036d">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>get_output_control()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a5c7d54f16fb0c778503371fd2115220d">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a7f102c853bf23c0bae7c787d93c8ef7e">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#aba7010d4b3206095c85180406e3b220b">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>get_packet()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a29d88bed13184a25269861da13441ab5">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>get_parameter_file()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a15c6b8501d7f08dccc12f1e8b903cacc">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a08dfee595c3bfd225a44d904d287e996">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a2fe857eb3b74576bc60b7756b90aa2c4">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>get_periodic_noisemap_store()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a28f320bcaeeafcaae613d033319857ed">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ab1e640e753806ac53563785bf0e43358">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ad5b4620d151598f1b65c7c7e7d092731">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>get_phase()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_ap_data.xhtml#afa327c53b6685f78b7160208de88878c">pymoduleconnector.moduleconnectorwrapper.BasebandApData</a>
</li>
<li>get_phase_noise_correction()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aa3096c3056b61f28ac47912adc824f7f">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>get_playback_rate()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_player.xhtml#a27453b9ee48f41462d2871500735b4e6">pymoduleconnector.moduleconnectorwrapper.PyDataPlayer</a>
</li>
<li>get_position()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_player.xhtml#a59cc70eb64d60f4a1c17ed7262b6e05c">pymoduleconnector.moduleconnectorwrapper.PyDataPlayer</a>
</li>
<li>get_profileid()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a6462020b272cf4b2843467d024ad4d26">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#adf08bec26c2042ae8f8420560c89ac26">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a0d3f5832a1594e274d5efc26a47011b6">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>get_Q()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_iq_data.xhtml#ab35202e2efc2e1504669d8c3bfd05e17">pymoduleconnector.moduleconnectorwrapper.BasebandIqData</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_float_data.xhtml#a91a7a8cf1d9a000dfcf353138c3c40d8">pymoduleconnector.moduleconnectorwrapper.RadarBasebandFloatData</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_q15_data.xhtml#a46e6f70585106d141c4930e310e0406c">pymoduleconnector.moduleconnectorwrapper.RadarBasebandQ15Data</a>
</li>
<li>get_register()
: <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_map.xhtml#a4d2f8916948a75fd99a13f1f77c59373">pymoduleconnector.extras.regmap.RegMap</a>
</li>
<li>get_respiration_data()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a95312b223059c2bb378a3e3b55b3db89">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>get_sensitivity()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ad530bc6eba3dc220cc01dfecf512873b">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ac4ca8d9c0cb9ba5da633a3d57305b487">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a093c0d01728225c5ebb766d09c279d9c">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>get_sensor_mode()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a1ab7e51ba68932e8f39ee6e04407a167">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a91bb2bce178f87de0ed0a61d97b495c8">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a660e47c44813d47ca034cda75b5e4d95">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>get_serial_number()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#ac7e832229d201de7a41ee2e5cf25ecbf">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>get_session_id()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml#a5dac7662b2bad5c8e1e43802576d29d4">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a44620f5f8a32092b863d78e6baac9ad0">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>
</li>
<li>get_size()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml#aa261009bec3bd73ec34d9e87e34f2872">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a>
</li>
<li>get_sleep_data()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a69795dfe5404e124ebd24e98826848bf">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
</li>
<li>get_start_epoch()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml#a7b1e58304ec531a1a024e7b3d4e9906d">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a>
</li>
<li>get_system_info()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a7e768e062888035dbd6a81d337e9e075">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aa825a63b84afa70e68616c11283e033a">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a358163cbc1dd10fa30679d7985db4d8e">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a40dc0d608675762cd9a9b8f9feb80e4b">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>get_tx_center_frequency()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#abc5c489f6e141f099fde86ff1a393ece">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a32cc1f185eb4f5e26c5c8211b244dbe0">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ae345e0319492b7b10d70e69b6c5a6b5c">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
</li>
<li>get_type()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml#a4b9d603ab5ae8fa32a5ac826a5babb89">pymoduleconnector.moduleconnectorwrapper.PreferredSplitSize</a>
</li>
<li>get_user_header()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml#a115bcb784f8909992cff589ec005722e">pymoduleconnector.moduleconnectorwrapper.RecordingOptions</a>
</li>
<li>get_x2m200()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml#a3c97acdd3f2e2209fcc9a50b44710b53">pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector</a>
</li>
<li>get_x4m200()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml#aa4144b8b9498c022d09fb4d530ab4585">pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector</a>
</li>
<li>get_x4m210()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml#afbb362e75bc9c9b6fac900543ba2e6a4">pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector</a>
</li>
<li>get_x4m300()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml#ac3e0b9af8b0b1688d540ebd1ee8eafdc">pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector</a>
</li>
<li>get_xep()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml#a6c38a6ca30e37b27412eef559f3dddd4">pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector</a>
</li>
<li>git_sha()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml#acb920ea6a4e1076f07063607d568dfab">pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
