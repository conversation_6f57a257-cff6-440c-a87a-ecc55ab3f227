<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnectorwrapper.SleepData Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_data.xhtml">SleepData</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_data-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.SleepData Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Represents the sleep status data coming from the module.  
 <a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_data.xhtml#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a8ae5d21a853b47d07b95d86702197f51"><td class="memItemLeft" align="right" valign="top"><a id="a8ae5d21a853b47d07b95d86702197f51"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_data.xhtml#a8ae5d21a853b47d07b95d86702197f51">__init__</a> (self)</td></tr>
<tr class="memdesc:a8ae5d21a853b47d07b95d86702197f51"><td class="mdescLeft">&#160;</td><td class="mdescRight"><b>init</b>(XeThru::SleepData self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_data.xhtml" title="Represents the sleep status data coming from the module. ">SleepData</a> <br /></td></tr>
<tr class="separator:a8ae5d21a853b47d07b95d86702197f51"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a93d8f4a524fbcd11923cfe5adcf94cb6"><td class="memItemLeft" align="right" valign="top"><a id="a93d8f4a524fbcd11923cfe5adcf94cb6"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>this</b></td></tr>
<tr class="separator:a93d8f4a524fbcd11923cfe5adcf94cb6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a8430dd9283a05afa2f5712fcd71e30d8"><td class="memItemLeft" align="right" valign="top"><a id="a8430dd9283a05afa2f5712fcd71e30d8"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>frame_counter</b> = _swig_property(_moduleconnectorwrapper.SleepData_frame_counter_get, _moduleconnectorwrapper.SleepData_frame_counter_set)</td></tr>
<tr class="separator:a8430dd9283a05afa2f5712fcd71e30d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a30e93253df05ea556ca69e51e2fbb1aa"><td class="memItemLeft" align="right" valign="top"><a id="a30e93253df05ea556ca69e51e2fbb1aa"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>sensor_state</b> = _swig_property(_moduleconnectorwrapper.SleepData_sensor_state_get, _moduleconnectorwrapper.SleepData_sensor_state_set)</td></tr>
<tr class="separator:a30e93253df05ea556ca69e51e2fbb1aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a548a6cc0dbf3a883beb008ec4ce70529"><td class="memItemLeft" align="right" valign="top"><a id="a548a6cc0dbf3a883beb008ec4ce70529"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>respiration_rate</b> = _swig_property(_moduleconnectorwrapper.SleepData_respiration_rate_get, _moduleconnectorwrapper.SleepData_respiration_rate_set)</td></tr>
<tr class="separator:a548a6cc0dbf3a883beb008ec4ce70529"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c8d7eb0924ed517733c971e03d11e8a"><td class="memItemLeft" align="right" valign="top"><a id="a6c8d7eb0924ed517733c971e03d11e8a"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>distance</b> = _swig_property(_moduleconnectorwrapper.SleepData_distance_get, _moduleconnectorwrapper.SleepData_distance_set)</td></tr>
<tr class="separator:a6c8d7eb0924ed517733c971e03d11e8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7a9a965ccdc0916789a135eb6cb2c482"><td class="memItemLeft" align="right" valign="top"><a id="a7a9a965ccdc0916789a135eb6cb2c482"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>signal_quality</b> = _swig_property(_moduleconnectorwrapper.SleepData_signal_quality_get, _moduleconnectorwrapper.SleepData_signal_quality_set)</td></tr>
<tr class="separator:a7a9a965ccdc0916789a135eb6cb2c482"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4cab1631e765eccefa78e7839c2f6b7a"><td class="memItemLeft" align="right" valign="top"><a id="a4cab1631e765eccefa78e7839c2f6b7a"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>movement_slow</b> = _swig_property(_moduleconnectorwrapper.SleepData_movement_slow_get, _moduleconnectorwrapper.SleepData_movement_slow_set)</td></tr>
<tr class="separator:a4cab1631e765eccefa78e7839c2f6b7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa157bdc73fab00c31b353b2c19e6eea"><td class="memItemLeft" align="right" valign="top"><a id="afa157bdc73fab00c31b353b2c19e6eea"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>movement_fast</b> = _swig_property(_moduleconnectorwrapper.SleepData_movement_fast_get, _moduleconnectorwrapper.SleepData_movement_fast_set)</td></tr>
<tr class="separator:afa157bdc73fab00c31b353b2c19e6eea"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Represents the sleep status data coming from the module. </p>
<h2>Attributes </h2>
<ul>
<li><p class="startli"><code>frame_counter</code> : <code>uint32_t</code> A sequential counter from the radar data.</p>
<p class="startli">Incremented for each captured frame.</p>
</li>
<li><code>sensor_state</code> : <code>uint32_t</code> This represent the steady state of the sensor module.</li>
<li><p class="startli"><code>respiration_rate</code> : <code>float</code> Respiration rate (respirations per minute / RPM).</p>
<p class="startli">Valid when SensorState is Breathing.</p>
</li>
<li><code>distance</code> : <code>float</code> Is the distance from the sensorto the subject (which the sensor is currently locked on to).</li>
<li><p class="startli"><code>signal_quality</code> : <code>uint32_t</code> Quality measure of the signal quality, describing the signal-to-noise ratio of the current respiration lock.</p>
<p class="startli">Value from 0 to 10, 0=low -&gt; 10=high.</p>
</li>
<li><p class="startli"><code>movement_slow</code> : <code>float</code> First movement metric which captures the larger movements.</p>
<p class="startli">It is given as a percentage(0-100). Higher the percentage larger the movement.</p>
</li>
<li><p class="startli"><code>movement_fast</code> : <code>float</code> Second movement metric which also captures the larger movements.</p>
<p class="startli">It is represented as a percentage (0-100). Higher the percentage larger the movement. This metric is more responsive than the MovementSlow. It captures the movements faster than the former.</p>
</li>
</ul>
<p>C++ includes: Data.hpp </p>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/moduleconnectorwrapper/__init__.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
