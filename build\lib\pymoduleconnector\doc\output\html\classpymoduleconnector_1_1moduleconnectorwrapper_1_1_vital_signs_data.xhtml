<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnectorwrapper.VitalSignsData Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_vital_signs_data.xhtml">VitalSignsData</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_vital_signs_data-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.VitalSignsData Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Various vital signs.  
 <a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_vital_signs_data.xhtml#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a29c45d0f21dbac8c1427f5ccef200da5"><td class="memItemLeft" align="right" valign="top"><a id="a29c45d0f21dbac8c1427f5ccef200da5"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_vital_signs_data.xhtml#a29c45d0f21dbac8c1427f5ccef200da5">__init__</a> (self)</td></tr>
<tr class="memdesc:a29c45d0f21dbac8c1427f5ccef200da5"><td class="mdescLeft">&#160;</td><td class="mdescRight"><b>init</b>(XeThru::VitalSignsData self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_vital_signs_data.xhtml" title="Various vital signs. ">VitalSignsData</a> <br /></td></tr>
<tr class="separator:a29c45d0f21dbac8c1427f5ccef200da5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:ad358d5f36337cf2682f8f8ada752323a"><td class="memItemLeft" align="right" valign="top"><a id="ad358d5f36337cf2682f8f8ada752323a"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>this</b></td></tr>
<tr class="separator:ad358d5f36337cf2682f8f8ada752323a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:adcb9b75a3cb9d23f9fecfcb15041b10a"><td class="memItemLeft" align="right" valign="top"><a id="adcb9b75a3cb9d23f9fecfcb15041b10a"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>frame_counter</b> = _swig_property(_moduleconnectorwrapper.VitalSignsData_frame_counter_get, _moduleconnectorwrapper.VitalSignsData_frame_counter_set)</td></tr>
<tr class="separator:adcb9b75a3cb9d23f9fecfcb15041b10a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae61d5159f608bf9d0fbb11bfef211ae5"><td class="memItemLeft" align="right" valign="top"><a id="ae61d5159f608bf9d0fbb11bfef211ae5"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>sensor_state</b> = _swig_property(_moduleconnectorwrapper.VitalSignsData_sensor_state_get, _moduleconnectorwrapper.VitalSignsData_sensor_state_set)</td></tr>
<tr class="separator:ae61d5159f608bf9d0fbb11bfef211ae5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaa4da7ecbb82c3bbea3a4cf97af45e74"><td class="memItemLeft" align="right" valign="top"><a id="aaa4da7ecbb82c3bbea3a4cf97af45e74"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>respiration_rate</b> = _swig_property(_moduleconnectorwrapper.VitalSignsData_respiration_rate_get, _moduleconnectorwrapper.VitalSignsData_respiration_rate_set)</td></tr>
<tr class="separator:aaa4da7ecbb82c3bbea3a4cf97af45e74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af4d6680d89c566d0d2d8df1a24eab883"><td class="memItemLeft" align="right" valign="top"><a id="af4d6680d89c566d0d2d8df1a24eab883"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>respiration_distance</b> = _swig_property(_moduleconnectorwrapper.VitalSignsData_respiration_distance_get, _moduleconnectorwrapper.VitalSignsData_respiration_distance_set)</td></tr>
<tr class="separator:af4d6680d89c566d0d2d8df1a24eab883"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f9e93281a750b20c249498c2317641c"><td class="memItemLeft" align="right" valign="top"><a id="a7f9e93281a750b20c249498c2317641c"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>respiration_confidence</b> = _swig_property(_moduleconnectorwrapper.VitalSignsData_respiration_confidence_get, _moduleconnectorwrapper.VitalSignsData_respiration_confidence_set)</td></tr>
<tr class="separator:a7f9e93281a750b20c249498c2317641c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ede427628a2f283555263528564ddc4"><td class="memItemLeft" align="right" valign="top"><a id="a1ede427628a2f283555263528564ddc4"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>heart_rate</b> = _swig_property(_moduleconnectorwrapper.VitalSignsData_heart_rate_get, _moduleconnectorwrapper.VitalSignsData_heart_rate_set)</td></tr>
<tr class="separator:a1ede427628a2f283555263528564ddc4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06c5b835d82818b2c1bc4cccbe7cca83"><td class="memItemLeft" align="right" valign="top"><a id="a06c5b835d82818b2c1bc4cccbe7cca83"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>heart_distance</b> = _swig_property(_moduleconnectorwrapper.VitalSignsData_heart_distance_get, _moduleconnectorwrapper.VitalSignsData_heart_distance_set)</td></tr>
<tr class="separator:a06c5b835d82818b2c1bc4cccbe7cca83"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2569e0f204576742105405d8a791f9e9"><td class="memItemLeft" align="right" valign="top"><a id="a2569e0f204576742105405d8a791f9e9"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>heart_confidence</b> = _swig_property(_moduleconnectorwrapper.VitalSignsData_heart_confidence_get, _moduleconnectorwrapper.VitalSignsData_heart_confidence_set)</td></tr>
<tr class="separator:a2569e0f204576742105405d8a791f9e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5bf3a9bfeb92716815a82faf6300d8d4"><td class="memItemLeft" align="right" valign="top"><a id="a5bf3a9bfeb92716815a82faf6300d8d4"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>normalized_movement_slow</b> = _swig_property(_moduleconnectorwrapper.VitalSignsData_normalized_movement_slow_get, _moduleconnectorwrapper.VitalSignsData_normalized_movement_slow_set)</td></tr>
<tr class="separator:a5bf3a9bfeb92716815a82faf6300d8d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11641ca4b3809f8a77bdf3dea07263dc"><td class="memItemLeft" align="right" valign="top"><a id="a11641ca4b3809f8a77bdf3dea07263dc"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>normalized_movement_fast</b> = _swig_property(_moduleconnectorwrapper.VitalSignsData_normalized_movement_fast_get, _moduleconnectorwrapper.VitalSignsData_normalized_movement_fast_set)</td></tr>
<tr class="separator:a11641ca4b3809f8a77bdf3dea07263dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb4a3c0e36ea29d4a5a4bbe56b604e77"><td class="memItemLeft" align="right" valign="top"><a id="abb4a3c0e36ea29d4a5a4bbe56b604e77"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>normalized_movement_start</b> = _swig_property(_moduleconnectorwrapper.VitalSignsData_normalized_movement_start_get, _moduleconnectorwrapper.VitalSignsData_normalized_movement_start_set)</td></tr>
<tr class="separator:abb4a3c0e36ea29d4a5a4bbe56b604e77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad6a5df2e62ee6579e45ccd860e8d905e"><td class="memItemLeft" align="right" valign="top"><a id="ad6a5df2e62ee6579e45ccd860e8d905e"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>normalized_movement_end</b> = _swig_property(_moduleconnectorwrapper.VitalSignsData_normalized_movement_end_get, _moduleconnectorwrapper.VitalSignsData_normalized_movement_end_set)</td></tr>
<tr class="separator:ad6a5df2e62ee6579e45ccd860e8d905e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Various vital signs. </p>
<h2>Parameters </h2>
<ul>
<li><code>frame_counter</code> : Frame counter.</li>
<li><code>sensor_state</code> : See XTS_VAL_RESP_STATE_* in xtid.h for meaning.</li>
<li><code>respiration_rate</code> : Respirations per minute</li>
<li><code>respiration_distance</code> : Distance to breather.</li>
<li><code>respiration_confidence</code> : Confidence. Not yet used.</li>
<li><code>heart_rate</code> : Beats per minute. Not yet used.</li>
<li><code>heart_distance</code> : Distance to target with pulse. Not yet used.</li>
<li><code>heart_confidence</code> : Confidence. Not yet used.</li>
<li><code>normalized_movement_slow</code> : Sum of movement within a fixed range from the target, slow integration.</li>
<li><code>normalized_movement_fast</code> : Sum of movement within a fixed range from the target, fast integration.</li>
<li><code>normalized_movement_start</code> : Start of the range to sum.</li>
<li><code>normalized_movement_end</code> : End of the range to sum.</li>
</ul>
<h2>Attributes </h2>
<ul>
<li><code>frame_counter</code> : <code>uint32_t</code></li>
<li><code>sensor_state</code> : <code>uint32_t</code></li>
<li><code>respiration_rate</code> : <code>float</code></li>
<li><code>respiration_distance</code> : <code>float</code></li>
<li><code>respiration_confidence</code> : <code>float</code></li>
<li><code>heart_rate</code> : <code>float</code></li>
<li><code>heart_distance</code> : <code>float</code></li>
<li><code>heart_confidence</code> : <code>float</code></li>
<li><code>normalized_movement_slow</code> : <code>float</code></li>
<li><code>normalized_movement_fast</code> : <code>float</code></li>
<li><code>normalized_movement_start</code> : <code>float</code></li>
<li><code>normalized_movement_end</code> : <code>float</code></li>
</ul>
<p>C++ includes: Data.hpp </p>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/moduleconnectorwrapper/__init__.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
