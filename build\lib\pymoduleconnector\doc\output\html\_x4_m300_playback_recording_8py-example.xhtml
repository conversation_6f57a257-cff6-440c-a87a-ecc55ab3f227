<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: X4M300_playback_recording.py</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">X4M300_playback_recording.py</div>  </div>
</div><!--header-->
<div class="contents">
<p>Target module: X4M300 (only data recorded by X4M300 needed)Introduction: This is an example of how to playback recoreded data.</p>
<p>Command to run: "python X4M300_plot_movementlist.py -f xethru_recording_meta.dat" or "python3 X4M300_plot_movementlist.py -f xethru_recording_meta.dat".</p>
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">#!/usr/bin/env python</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"># -*- coding: utf-8 -*-</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="stringliteral">&quot;&quot;&quot; \example X4M300_playback_recording.py</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="stringliteral">#Target module: X4M300 (only data recorded by X4M300 needed) </span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="stringliteral">#Introduction: This is an example of how to playback recoreded data.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="stringliteral">#Command to run: &quot;python X4M300_plot_movementlist.py -f xethru_recording_meta.dat&quot; or &quot;python3 X4M300_plot_movementlist.py -f xethru_recording_meta.dat&quot;.</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="stringliteral">&quot;&quot;&quot;</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;</div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="keyword">from</span> __future__ <span class="keyword">import</span> print_function</div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="keyword">from</span> pymoduleconnector <span class="keyword">import</span> ModuleConnector</div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="keyword">from</span> pymoduleconnector <span class="keyword">import</span> DataPlayer</div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="keyword">from</span> pymoduleconnector <span class="keyword">import</span> DataType</div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;</div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="keyword">from</span> optparse <span class="keyword">import</span> OptionParser</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="keyword">from</span> datetime <span class="keyword">import</span> datetime</div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;</div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="keyword">import</span> sys</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="keyword">import</span> time</div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="keyword">def </span>start_playback(meta_filename, depth):</div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;    print(<span class="stringliteral">&quot;start playback from: {}, depth: {}&quot;</span>.format(meta_filename, depth))</div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;</div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;    player = DataPlayer(meta_filename, depth)</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;    mc = ModuleConnector(player, log_level=0)</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;    <span class="comment"># Get read-only interface and receive telegrams / binary packets from recording</span></div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;    x4m300 = mc.get_x4m300()</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;    <span class="comment"># Control output</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;    player.set_filter(DataType.BasebandIqDataType | DataType.PresenceSingleDataType);</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;    player.play()</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;    <span class="comment"># ...</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;    player.pause();</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;    <span class="comment"># ...</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;    player.set_playback_rate(2.0);</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;    player.set_playback_rate(1.0);</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;    player.set_loop_mode_enabled(<span class="keyword">True</span>);</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;    player.play();</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;    <span class="keywordflow">try</span>:</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;        <span class="keywordflow">while</span> <span class="keyword">True</span>:</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;            <span class="keywordflow">if</span> x4m300.peek_message_baseband_iq():</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;                data = x4m300.read_message_baseband_iq()</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;                print(<span class="stringliteral">&quot;received baseband iq data, frame counter: {}&quot;</span>.format(data.frame_counter))</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;            <span class="keywordflow">if</span> x4m300.peek_message_presence_single():</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;                data = x4m300.read_message_presence_single()</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;                print(<span class="stringliteral">&quot;received presence single data, frame counter: {}&quot;</span>.format(data.frame_counter))</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;            time.sleep(0.02) <span class="comment"># Sleep 20 ms</span></div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;    <span class="keywordflow">except</span> (KeyboardInterrupt, SystemExit):</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        del mc</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        <span class="keywordflow">raise</span></div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="keyword">def </span>main():</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    parser = OptionParser()</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;    parser.add_option(<span class="stringliteral">&quot;-f&quot;</span>, <span class="stringliteral">&quot;--file&quot;</span>, dest=<span class="stringliteral">&quot;meta_filename&quot;</span>, metavar=<span class="stringliteral">&quot;FILE&quot;</span>,\</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;                      help=<span class="stringliteral">&quot;meta file from recording&quot;</span>)</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;    parser.add_option(<span class="stringliteral">&quot;--depth&quot;</span>, dest=<span class="stringliteral">&quot;depth&quot;</span>, metavar=<span class="stringliteral">&quot;NUMBER&quot;</span>, type=<span class="stringliteral">&quot;int&quot;</span>, default=-1,\</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;                      help=<span class="stringliteral">&quot;number of meta files to read in chained mode&quot;</span>)</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;    (options, args) = parser.parse_args()</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;    <span class="keywordflow">if</span> <span class="keywordflow">not</span> options.meta_filename:</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        print(<span class="stringliteral">&quot;Please specify a &#39;xethru_recording_meta.dat&#39; file (use -f &lt;file&gt; or --file &lt;file&gt;)&quot;</span>)</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        sys.exit(1)</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;    start_playback(options.meta_filename, options.depth)</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;<span class="keywordflow">if</span> __name__ == <span class="stringliteral">&quot;__main__&quot;</span>:</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;    main()</div></div><!-- fragment --> </div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
