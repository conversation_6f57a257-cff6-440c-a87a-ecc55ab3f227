<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: X4M200_X4M300_printout_pulsedoppler_data.py</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">X4M200_X4M300_printout_pulsedoppler_data.py</div>  </div>
</div><!--header-->
<div class="contents">
<p>Target module: X4M200,X4M300Introduction: This is an example of how to set up a module for streaming pulse-Doppler telegrams, and how to assemble them to whole range-Doppler matrices.</p>
<p>Command to run: "python X4M200_X4M300_printout_pulsedoppler_data.py -d com8" or "python3 X4M200_X4M300_printout_pulsedoppler_data.py -d com8" change "com8" with your device name, using "--help" to see other options. Using TCP server address as device name is also supported, e.g. "python X4M200_sleep_record.py -d tcp://*************:3000".</p>
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">#!/usr/bin/env python</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"># -*- coding: utf-8 -*-</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="stringliteral">&quot;&quot;&quot; \example X4M200_X4M300_printout_pulsedoppler_data.py</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="stringliteral">#Target module: X4M200,X4M300</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="stringliteral">#Introduction:  This is an example of how to set up a module for streaming pulse-Doppler</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="stringliteral">               telegrams, and how to assemble them to whole range-Doppler matrices.</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="stringliteral">               </span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="stringliteral">#Command to run:  &quot;python X4M200_X4M300_printout_pulsedoppler_data.py -d com8&quot; or &quot;python3 X4M200_X4M300_printout_pulsedoppler_data.py -d com8&quot;</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="stringliteral">                  change &quot;com8&quot; with your device name, using &quot;--help&quot; to see other options. </span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="stringliteral">                 Using TCP server address as device name is also supported, e.g. </span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="stringliteral">                 &quot;python X4M200_sleep_record.py -d tcp://*************:3000&quot;.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="stringliteral">&quot;&quot;&quot;</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="keyword">from</span> __future__ <span class="keyword">import</span> print_function</div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="keyword">import</span> numpy <span class="keyword">as</span> np</div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="keyword">import</span> pymoduleconnector</div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="keyword">from</span> <a class="code" href="namespacepymoduleconnector_1_1ids.xhtml">pymoduleconnector.ids</a> <span class="keyword">import</span> *</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="keyword">from</span> optparse <span class="keyword">import</span> OptionParser</div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="keyword">import</span> sys</div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="keyword">import</span> time</div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;</div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;parser = OptionParser()</div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;parser.add_option(</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;    <span class="stringliteral">&quot;-d&quot;</span>,</div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;    <span class="stringliteral">&quot;--device&quot;</span>,</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;    dest=<span class="stringliteral">&quot;device_name&quot;</span>,</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;    help=<span class="stringliteral">&quot;device file to use, example: python %s -d COM4&quot;</span>%sys.argv[0],</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;    metavar=<span class="stringliteral">&quot;FILE&quot;</span>)</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;parser.add_option(</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;    <span class="stringliteral">&quot;-i&quot;</span>,</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;    <span class="stringliteral">&quot;--interface&quot;</span>,</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;    dest=<span class="stringliteral">&quot;interface&quot;</span>,</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;    default=<span class="stringliteral">&quot;x4m300&quot;</span>,</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;    help=<span class="stringliteral">&quot;Interface to use. x4m300 or x4m200&quot;</span>,</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;    metavar=<span class="stringliteral">&quot;IF&quot;</span>)</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;parser.add_option(</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;    <span class="stringliteral">&quot;-w&quot;</span>,</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;    <span class="stringliteral">&quot;--datatype&quot;</span>,</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;    dest=<span class="stringliteral">&quot;datatype&quot;</span>,</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;    default=<span class="stringliteral">&quot;pulsedoppler&quot;</span>,</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;    help=<span class="stringliteral">&quot;Data to get. pulsedoppler or noisemap&quot;</span>,</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;    metavar=<span class="stringliteral">&quot;TYPE&quot;</span>)</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;parser.add_option(</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;    <span class="stringliteral">&quot;-f&quot;</span>,</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;    <span class="stringliteral">&quot;--format&quot;</span>,</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;    dest=<span class="stringliteral">&quot;format&quot;</span>,</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;    default=<span class="stringliteral">&quot;byte&quot;</span>,</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;    help=<span class="stringliteral">&quot;Data format to get. byte or float&quot;</span>,</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;    metavar=<span class="stringliteral">&quot;FORMAT&quot;</span>)</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;parser.add_option(</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;    <span class="stringliteral">&quot;-m&quot;</span>,</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;    <span class="stringliteral">&quot;--dopplers&quot;</span>,</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    dest=<span class="stringliteral">&quot;dopplers&quot;</span>,</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;    default=<span class="stringliteral">&quot;both&quot;</span>,</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;    help=<span class="stringliteral">&quot;Which pD instance to get. fast, slow or both.&quot;</span>,</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;    metavar=<span class="stringliteral">&quot;FORMAT&quot;</span>)</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;parser.add_option(</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;    <span class="stringliteral">&quot;-n&quot;</span>,</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;    <span class="stringliteral">&quot;--num-messages&quot;</span>,</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;    dest=<span class="stringliteral">&quot;num_messages&quot;</span>,</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;    type=int,</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;    default=0,</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;    help=<span class="stringliteral">&quot;how many matrices to read (0 = infinite)&quot;</span>,</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;    metavar=<span class="stringliteral">&quot;INT&quot;</span>)</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;(options, args) = parser.parse_args()</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;<span class="keywordflow">if</span> <span class="keywordflow">not</span> options.device_name:</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;    print(<span class="stringliteral">&quot;Please specify a device name, example: python %s -d COM4&quot;</span>%sys.argv[0])</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;    sys.exit(1)</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;mc = <a class="code" href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml">pymoduleconnector.ModuleConnector</a>(options.device_name, 0)</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;time.sleep(1)</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="keywordflow">if</span> options.interface == <span class="stringliteral">&quot;x4m300&quot;</span>:</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;    app = mc.get_x4m300()</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    app.ping()</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    app.load_profile(XTS_ID_APP_PRESENCE_2)</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;<span class="keywordflow">elif</span> options.interface == <span class="stringliteral">&quot;x4m200&quot;</span>:</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;    app = mc.get_x4m200()</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    app.ping()</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    app.load_profile(XTS_ID_APP_RESPIRATION_2)</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;<span class="keywordflow">else</span>:</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;    print(<span class="stringliteral">&quot;Interface not recognized.&quot;</span>, file=sys.stderr)</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    <span class="keywordflow">raise</span> SystemExit(1)</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;<span class="comment"># Flush all buffers</span></div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;<span class="keywordflow">while</span> app.peek_message_pulsedoppler_byte():</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;    app.read_message_pulsedoppler_byte()</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;<span class="keywordflow">while</span> app.peek_message_pulsedoppler_float():</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;    app.read_message_pulsedoppler_float()</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;<span class="keywordflow">while</span> app.peek_message_noisemap_byte():</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;    app.read_message_noisemap_byte()</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;<span class="keywordflow">while</span> app.peek_message_noisemap_float():</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;    app.read_message_noisemap_float()</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;<span class="comment"># Turn on outputs.</span></div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="keywordflow">if</span> options.dopplers == <span class="stringliteral">&quot;both&quot;</span>:</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;    ctrl = XTID_OUTPUT_CONTROL_PD_FAST_ENABLE | XTID_OUTPUT_CONTROL_PD_SLOW_ENABLE</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;<span class="keywordflow">elif</span> options.dopplers == <span class="stringliteral">&quot;fast&quot;</span>:</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;    ctrl = XTID_OUTPUT_CONTROL_PD_FAST_ENABLE</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;<span class="keywordflow">elif</span> options.dopplers == <span class="stringliteral">&quot;slow&quot;</span>:</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;    ctrl = XTID_OUTPUT_CONTROL_PD_SLOW_ENABLE</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;<span class="keywordflow">else</span>:</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;    print(<span class="stringliteral">&quot;Pulse-Doppler instance not recognized.&quot;</span>, file=sys.stderr)</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;    <span class="keywordflow">raise</span> SystemExit(1)</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;<span class="keywordflow">if</span> options.datatype == <span class="stringliteral">&quot;pulsedoppler&quot;</span> <span class="keywordflow">and</span> options.format == <span class="stringliteral">&quot;byte&quot;</span>:</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;    app.set_output_control(XTS_ID_PULSEDOPPLER_BYTE, ctrl)</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;<span class="keywordflow">elif</span> options.datatype == <span class="stringliteral">&quot;pulsedoppler&quot;</span> <span class="keywordflow">and</span> options.format == <span class="stringliteral">&quot;float&quot;</span>:</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;    app.set_output_control(XTS_ID_PULSEDOPPLER_FLOAT, ctrl)</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="keywordflow">elif</span> options.datatype == <span class="stringliteral">&quot;noisemap&quot;</span> <span class="keywordflow">and</span> options.format == <span class="stringliteral">&quot;byte&quot;</span>:</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    app.set_output_control(XTS_ID_NOISEMAP_BYTE, ctrl)</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="keywordflow">elif</span> options.datatype == <span class="stringliteral">&quot;noisemap&quot;</span> <span class="keywordflow">and</span> options.format == <span class="stringliteral">&quot;float&quot;</span>:</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;    app.set_output_control(XTS_ID_NOISEMAP_FLOAT, ctrl)</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;<span class="keywordflow">else</span>:</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    print(<span class="stringliteral">&quot;Datatype/format not recognized.&quot;</span>, file=sys.stderr)</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;    <span class="keywordflow">raise</span> SystemExit(1)</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;<span class="comment"># Start sensor</span></div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;app.set_sensor_mode(XTS_SM_RUN, 0)</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;<span class="comment"># Set up state machinery for matrix assembly</span></div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;matrix = []</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;is_negative = <span class="keyword">True</span></div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;prev_range_idx = -1</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;ok = <span class="keyword">True</span></div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;instance = <span class="keywordtype">None</span></div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;n_printed = 0</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;got_errors = <span class="keyword">False</span></div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;<span class="comment"># Assemble whole range-Doppler matrices from the individual packets</span></div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;<span class="keywordflow">while</span> <span class="keyword">True</span>:</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;    <span class="keywordflow">if</span> options.datatype == <span class="stringliteral">&quot;pulsedoppler&quot;</span> <span class="keywordflow">and</span> options.format == <span class="stringliteral">&quot;byte&quot;</span>:</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;        pkt = app.read_message_pulsedoppler_byte()</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;    <span class="keywordflow">elif</span> options.datatype == <span class="stringliteral">&quot;pulsedoppler&quot;</span> <span class="keywordflow">and</span> options.format == <span class="stringliteral">&quot;float&quot;</span>:</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;        pkt = app.read_message_pulsedoppler_float()</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;    <span class="keywordflow">elif</span> options.datatype == <span class="stringliteral">&quot;noisemap&quot;</span> <span class="keywordflow">and</span> options.format == <span class="stringliteral">&quot;byte&quot;</span>:</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;        pkt = app.read_message_noisemap_byte()</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    <span class="keywordflow">elif</span> options.datatype == <span class="stringliteral">&quot;noisemap&quot;</span> <span class="keywordflow">and</span> options.format == <span class="stringliteral">&quot;float&quot;</span>:</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;        pkt = app.read_message_noisemap_float()</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;    <span class="keywordflow">if</span> instance <span class="keywordflow">is</span> <span class="keywordtype">None</span>:</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;        instance = pkt.pulsedoppler_instance</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;    <span class="keywordflow">if</span> pkt.range_idx == 0 <span class="keywordflow">and</span> is_negative <span class="keywordflow">and</span> matrix:</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        <span class="comment"># We now have a complete range-Doppler matrix.</span></div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;        matrix = np.array(matrix)</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;        print(<span class="stringliteral">&quot;Instance:&quot;</span>, [<span class="stringliteral">&quot;slow&quot;</span>, <span class="stringliteral">&quot;fast&quot;</span>][instance], matrix.shape)</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;        print(matrix)</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;        n_printed += 1</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        <span class="keywordflow">if</span> options.num_messages != 0 <span class="keywordflow">and</span> n_printed &gt;= options.num_messages:</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;            <span class="keywordflow">break</span></div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;        <span class="comment"># Reset</span></div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;        matrix = []</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;        prev_range_idx = -1</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;        instance = pkt.pulsedoppler_instance</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;    <span class="keywordflow">if</span> is_negative <span class="keywordflow">and</span> pkt.range_idx == prev_range_idx + 1:</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        ok = <span class="keyword">True</span></div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;        prev_range_idx = pkt.range_idx</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;    <span class="keywordflow">elif</span> <span class="keywordflow">not</span> is_negative <span class="keywordflow">and</span> pkt.range_idx == prev_range_idx:</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;        <span class="keywordflow">pass</span></div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;    <span class="keywordflow">else</span>:</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;        <span class="keywordflow">if</span> ok:</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;            print(<span class="stringliteral">&quot;bad range_idx &quot;</span>, pkt.range_idx, <span class="stringliteral">&quot; in instance &quot;</span>, instance,</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;                    <span class="stringliteral">&quot;, prev &quot;</span>, prev_range_idx ,<span class="stringliteral">&quot;, resetting and waiting for &quot;</span></div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;                    <span class="stringliteral">&quot;zero.&quot;</span>, sep=<span class="stringliteral">&quot;&quot;</span>)</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;            matrix = []</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;            is_negative = <span class="keyword">True</span></div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;            prev_range_idx = -1</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;            ok = <span class="keyword">False</span></div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;            got_errors = <span class="keyword">True</span></div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;        <span class="keywordflow">continue</span></div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;    <span class="keywordflow">if</span> options.format == <span class="stringliteral">&quot;byte&quot;</span>:</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;        <span class="comment"># Convert byte data to float.</span></div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;        data = bytearray(pkt.get_data())</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;        offset = pkt.byte_step_start</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;        step = pkt.byte_step_size</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;        data = [10.0**((float(x)*step + offset)/10.0) <span class="keywordflow">for</span> x <span class="keywordflow">in</span> data]</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;    <span class="keywordflow">else</span>:</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;        data = list(pkt.get_data())</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;    <span class="comment"># A noisemap misses a piece in the middle, so fill it with zeroes</span></div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;    <span class="comment"># Has no effect if nothing is missing.</span></div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;    <span class="keywordflow">if</span> is_negative:</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;        pkt_end = pkt.frequency_start + pkt.frequency_step * pkt.frequency_count</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;        <span class="keywordflow">if</span> pkt_end &lt; 0.000001:</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;            data.extend(0 <span class="keywordflow">for</span> _ <span class="keywordflow">in</span> range(int(round(-pkt_end / pkt.frequency_step))))</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;    <span class="keywordflow">else</span>:</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;        <span class="keywordflow">if</span> pkt.frequency_start &gt; 0.000001:</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;            pre = [0 <span class="keywordflow">for</span> _ <span class="keywordflow">in</span> range(int(round(pkt.frequency_start / pkt.frequency_step)))]</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;            data = pre + data</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;    <span class="keywordflow">if</span> len(matrix) &lt;= pkt.range_idx:</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;        matrix.append([])</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;    matrix[pkt.range_idx].extend(data)</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;    is_negative ^= <span class="keyword">True</span></div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;app.set_sensor_mode(XTS_SM_STOP, 0)</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;<span class="keywordflow">if</span> got_errors:</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;    sys.exit(1)</div></div><!-- fragment --> </div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
