<map id="Graphical Class Hierarchy" name="Graphical Class Hierarchy">
<area shape="rect" id="node2" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_ap_data.xhtml" title="pymoduleconnector.moduleconnectorwrapper.\lBasebandApData" alt="" coords="341,5,629,47"/>
<area shape="rect" id="node3" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_iq_data.xhtml" title="pymoduleconnector.moduleconnectorwrapper.\lBasebandIqData" alt="" coords="341,71,629,112"/>
<area shape="rect" id="node4" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_data_record.xhtml" title="Encapsulates data and information about one data record on disk. " alt="" coords="341,136,629,177"/>
<area shape="rect" id="node5" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_data_type.xhtml" title="Proxy of C++ XeThru::DataType class. " alt="" coords="341,201,629,243"/>
<area shape="rect" id="node6" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone.xhtml" title="Representation of the detection zone. " alt="" coords="341,267,629,308"/>
<area shape="rect" id="node7" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone_limits.xhtml" title="Is an aggrgation of parameters used to represent the detection zone limits. " alt="" coords="341,332,629,373"/>
<area shape="rect" id="node8" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_files.xhtml" title="pymoduleconnector.moduleconnectorwrapper.\lFiles" alt="" coords="341,397,629,439"/>
<area shape="rect" id="node9" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_frame_area.xhtml" title="pymoduleconnector.moduleconnectorwrapper.\lFrameArea" alt="" coords="341,463,629,504"/>
<area shape="rect" id="node10" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_io_pin_control.xhtml" title="Representation of io pin control configuration. " alt="" coords="341,528,629,569"/>
<area shape="rect" id="node11" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_periodic_noisemap_store.xhtml" title="Representation of periodic noisemap store parameters. " alt="" coords="341,593,629,635"/>
<area shape="rect" id="node12" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_preferred_split_size.xhtml" title="The PreferredSplitSize class allows specifying a split size. " alt="" coords="341,659,629,700"/>
<area shape="rect" id="node13" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_byte_data.xhtml" title="Represents one half or one range bin of pulse&#45;Doppler in byte format. " alt="" coords="341,724,629,765"/>
<area shape="rect" id="node14" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_float_data.xhtml" title="Represents one half or one range bin of pulse&#45;Doppler in float format. " alt="" coords="341,789,629,831"/>
<area shape="rect" id="node15" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_player.xhtml" title="pymoduleconnector.moduleconnectorwrapper.\lPyDataPlayer" alt="" coords="341,855,629,896"/>
<area shape="rect" id="node16" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml" title="The PyDataReader class allows reading of xethru data records from a recording. " alt="" coords="341,920,629,961"/>
<area shape="rect" id="node17" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_recorder.xhtml" title="The DataRecorder class allows recording of xethru data types. " alt="" coords="341,985,629,1027"/>
<area shape="rect" id="node18" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml" title="This class is responsible for establishing contact with the XeThru module. " alt="" coords="341,1051,629,1092"/>
<area shape="rect" id="node19" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml" title="Interface to the Xethru X2M200 Application module. " alt="" coords="341,1116,629,1157"/>
<area shape="rect" id="node20" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml" title="C++ includes: PyX4M200.hpp. " alt="" coords="341,1181,629,1223"/>
<area shape="rect" id="node21" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml" title="Proxy of C++ XeThru::PyX4M210 class. " alt="" coords="341,1247,629,1288"/>
<area shape="rect" id="node22" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml" title="C++ includes: PyX4M300.hpp. " alt="" coords="341,1312,629,1353"/>
<area shape="rect" id="node23" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml" title="C++ includes: PyXEP.hpp. " alt="" coords="341,1377,629,1419"/>
<area shape="rect" id="node24" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_float_data.xhtml" title="pymoduleconnector.moduleconnectorwrapper.\lRadarBasebandFloatData" alt="" coords="341,1443,629,1484"/>
<area shape="rect" id="node25" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_q15_data.xhtml" title="pymoduleconnector.moduleconnectorwrapper.\lRadarBasebandQ15Data" alt="" coords="341,1508,629,1549"/>
<area shape="rect" id="node26" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_data.xhtml" title="pymoduleconnector.moduleconnectorwrapper.\lRadarRfData" alt="" coords="341,1573,629,1615"/>
<area shape="rect" id="node27" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml" title="pymoduleconnector.moduleconnectorwrapper.\lRadarRfNormalizedData" alt="" coords="341,1639,629,1680"/>
<area shape="rect" id="node28" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_recording_options.xhtml" title="The RecordingOptions class allows specifying options for recording. " alt="" coords="341,1704,629,1745"/>
<area shape="rect" id="node29" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_respiration_data.xhtml" title="Represents the respiration status data coming from the module. " alt="" coords="341,1769,629,1811"/>
<area shape="rect" id="node30" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_data.xhtml" title="Represents the sleep status data coming from the module. " alt="" coords="341,1835,629,1876"/>
<area shape="rect" id="node31" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_stage_data.xhtml" title="pymoduleconnector.moduleconnectorwrapper.\lSleepStageData" alt="" coords="341,1900,629,1941"/>
<area shape="rect" id="node32" href="$classpymoduleconnector_1_1moduleconnectorwrapper_1_1_vital_signs_data.xhtml" title="Various vital signs. " alt="" coords="341,1965,629,2007"/>
</map>
