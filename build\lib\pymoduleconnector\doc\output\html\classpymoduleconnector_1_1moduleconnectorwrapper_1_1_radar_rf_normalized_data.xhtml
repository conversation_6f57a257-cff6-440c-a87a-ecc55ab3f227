<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">RadarRfNormalizedData</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData Class Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a4d149b9b72cc37d6ce591f4a742b9118"><td class="memItemLeft" align="right" valign="top"><a id="a4d149b9b72cc37d6ce591f4a742b9118"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>__init__</b> (self, args)</td></tr>
<tr class="separator:a4d149b9b72cc37d6ce591f4a742b9118"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a542fb2c1ddede9a8cffeca3473af6eef"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml#a542fb2c1ddede9a8cffeca3473af6eef">get_data</a> (self)</td></tr>
<tr class="memdesc:a542fb2c1ddede9a8cffeca3473af6eef"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_data(RadarRfNormalizedData self) -&gt; FloatVector  <a href="#a542fb2c1ddede9a8cffeca3473af6eef">More...</a><br /></td></tr>
<tr class="separator:a542fb2c1ddede9a8cffeca3473af6eef"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a5da7c3714fe5312f07a6a43a2fc19733"><td class="memItemLeft" align="right" valign="top"><a id="a5da7c3714fe5312f07a6a43a2fc19733"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>this</b></td></tr>
<tr class="separator:a5da7c3714fe5312f07a6a43a2fc19733"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a31b937cbdb9088eaf7345b29ee0ad419"><td class="memItemLeft" align="right" valign="top"><a id="a31b937cbdb9088eaf7345b29ee0ad419"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>frame_counter</b> = _swig_property(_moduleconnectorwrapper.RadarRfNormalizedData_frame_counter_get, _moduleconnectorwrapper.RadarRfNormalizedData_frame_counter_set)</td></tr>
<tr class="separator:a31b937cbdb9088eaf7345b29ee0ad419"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ee9c1b6acbf066f7aa1723cd8ea7161"><td class="memItemLeft" align="right" valign="top"><a id="a9ee9c1b6acbf066f7aa1723cd8ea7161"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>num_bins</b> = _swig_property(_moduleconnectorwrapper.RadarRfNormalizedData_num_bins_get, _moduleconnectorwrapper.RadarRfNormalizedData_num_bins_set)</td></tr>
<tr class="separator:a9ee9c1b6acbf066f7aa1723cd8ea7161"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a704b937ffae8092581bb31d2dc2d8c59"><td class="memItemLeft" align="right" valign="top"><a id="a704b937ffae8092581bb31d2dc2d8c59"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>bin_length</b> = _swig_property(_moduleconnectorwrapper.RadarRfNormalizedData_bin_length_get, _moduleconnectorwrapper.RadarRfNormalizedData_bin_length_set)</td></tr>
<tr class="separator:a704b937ffae8092581bb31d2dc2d8c59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad6d0b56475c9153ea108a3e9a770a004"><td class="memItemLeft" align="right" valign="top"><a id="ad6d0b56475c9153ea108a3e9a770a004"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>sample_frequency</b> = _swig_property(_moduleconnectorwrapper.RadarRfNormalizedData_sample_frequency_get, _moduleconnectorwrapper.RadarRfNormalizedData_sample_frequency_set)</td></tr>
<tr class="separator:ad6d0b56475c9153ea108a3e9a770a004"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d3de66d3b19267443181130b8b9a4f3"><td class="memItemLeft" align="right" valign="top"><a id="a0d3de66d3b19267443181130b8b9a4f3"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>carrier_frequency</b> = _swig_property(_moduleconnectorwrapper.RadarRfNormalizedData_carrier_frequency_get, _moduleconnectorwrapper.RadarRfNormalizedData_carrier_frequency_set)</td></tr>
<tr class="separator:a0d3de66d3b19267443181130b8b9a4f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af169719f1d4ee600df69f746c143d980"><td class="memItemLeft" align="right" valign="top"><a id="af169719f1d4ee600df69f746c143d980"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>frames_per_second</b> = _swig_property(_moduleconnectorwrapper.RadarRfNormalizedData_frames_per_second_get, _moduleconnectorwrapper.RadarRfNormalizedData_frames_per_second_set)</td></tr>
<tr class="separator:af169719f1d4ee600df69f746c143d980"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae499211eb4999909da0862e35dfeec29"><td class="memItemLeft" align="right" valign="top"><a id="ae499211eb4999909da0862e35dfeec29"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>range_offset</b> = _swig_property(_moduleconnectorwrapper.RadarRfNormalizedData_range_offset_get, _moduleconnectorwrapper.RadarRfNormalizedData_range_offset_set)</td></tr>
<tr class="separator:ae499211eb4999909da0862e35dfeec29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0679e0f9159dc87242c89f1080550027"><td class="memItemLeft" align="right" valign="top"><a id="a0679e0f9159dc87242c89f1080550027"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>data</b> = _swig_property(_moduleconnectorwrapper.RadarRfNormalizedData_data_get, _moduleconnectorwrapper.RadarRfNormalizedData_data_set)</td></tr>
<tr class="separator:a0679e0f9159dc87242c89f1080550027"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a542fb2c1ddede9a8cffeca3473af6eef"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a542fb2c1ddede9a8cffeca3473af6eef">&sect;&nbsp;</a></span>get_data()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData.get_data </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_data(RadarRfNormalizedData self) -&gt; FloatVector </p>
<p>Returns a reference to the data vector. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/moduleconnectorwrapper/__init__.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
