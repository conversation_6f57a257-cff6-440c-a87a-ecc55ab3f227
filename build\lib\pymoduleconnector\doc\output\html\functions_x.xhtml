<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_x"></a>- x -</h3><ul>
<li>x4driver_get_dac_max()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a984b5fbf71975c133587c42898f345b6">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_get_dac_min()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a0913355890bfe41b3ac366ff6dc2855a">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_get_downconversion()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a2ea1f541c7a932fb70e9a8d1bbf52b1c">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_get_fps()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a5722506858ef149aa6378ccc4198f6a8">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_get_frame_area()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a2cea78ca0d1d5f0da052f8c43e3de561">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_get_frame_area_offset()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#adce1046f11e71389f7ee140e412f3cce">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_get_frame_bin_count()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a9c7f615c64a64a8d269d412f42f266cc">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_get_iterations()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a8970ecf92434b784532af8cd4ac2fa7d">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_get_pif_register()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a901c25f7671004540dce574f1489a265">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_get_prf_div()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#adc1d77e574da6625e2599f4e2fa3f919">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_get_pulses_per_step()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ade000d1c27bdc2a5ab625b3494d69e6c">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_get_spi_register()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a905538453d91219746b56abbfc154155">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_get_tx_center_frequency()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ae3f60350e1d841761c398b949f87a333">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_get_tx_power()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a27ae89ef7b96a97b0145a748c56d952b">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_get_xif_register()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aace917a03ff13b718feac514d3944f97">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_init()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a3e0428c94e707a3bd1dd3d42c372df51">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_read_from_i2c_register()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ad37b0d7982bde72396eaaa1478405d69">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_read_from_spi_register()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a3d299d21f05a44cfc5c7254f10427e52">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_dac_max()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aec3612495db6fff46220706c4f9c2f4d">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_dac_min()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#abc715caef2f826b94ef3287aa153e79e">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_downconversion()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a82524a9b27ab7552d999aa4b81c38cdb">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_enable()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a7a7d6315f79e9e12995a884d393152b9">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_fps()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a68459e2f2ee2ee0894d7df61c1757c6c">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_frame_area()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ab3ce265886f14f7f376e5d46b0231e0f">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_frame_area_offset()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1fba61764d7abd07669e750cfe9e6981">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_iterations()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ae645c78af02a359c9f3112a664f509ca">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_pif_register()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ac94aba38f8e7df5c5cb16a4378e37037">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_prf_div()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1107468d06783110614842debf976d46">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_pulses_per_step()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#adb925124cc48218a36c4085c5fdb83f9">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_spi_register()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a81a294b61ffa595ba5d3a36ca7aaa83d">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_tx_center_frequency()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a8d091bb042a22eb510ef2b3bb68fa7f4">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_tx_power()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#abcc14bad0b9fa3390d79d42548473afe">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_set_xif_register()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a2f1227b5306335abd50e4b41d6bc10f8">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_write_to_i2c_register()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1db60ed9c0b778ab717a6f6f6fb5ebc8">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
<li>x4driver_write_to_spi_register()
: <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a908f314d1aa4b2e6acac726883c66327">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
