<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnectorwrapper.RespirationData Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_respiration_data.xhtml">RespirationData</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_respiration_data-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.RespirationData Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Represents the respiration status data coming from the module.  
 <a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_respiration_data.xhtml#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a4e43af1e233d47eb48872e86648b3054"><td class="memItemLeft" align="right" valign="top"><a id="a4e43af1e233d47eb48872e86648b3054"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_respiration_data.xhtml#a4e43af1e233d47eb48872e86648b3054">__init__</a> (self)</td></tr>
<tr class="memdesc:a4e43af1e233d47eb48872e86648b3054"><td class="mdescLeft">&#160;</td><td class="mdescRight"><b>init</b>(XeThru::RespirationData self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_respiration_data.xhtml" title="Represents the respiration status data coming from the module. ">RespirationData</a> <br /></td></tr>
<tr class="separator:a4e43af1e233d47eb48872e86648b3054"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:ae4804ac92e11516af5ffa850faa8be14"><td class="memItemLeft" align="right" valign="top"><a id="ae4804ac92e11516af5ffa850faa8be14"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>this</b></td></tr>
<tr class="separator:ae4804ac92e11516af5ffa850faa8be14"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a7845f019206bc74f853d7cc384104518"><td class="memItemLeft" align="right" valign="top"><a id="a7845f019206bc74f853d7cc384104518"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>frame_counter</b> = _swig_property(_moduleconnectorwrapper.RespirationData_frame_counter_get, _moduleconnectorwrapper.RespirationData_frame_counter_set)</td></tr>
<tr class="separator:a7845f019206bc74f853d7cc384104518"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b817690be79a6d801bb73e90c33eb70"><td class="memItemLeft" align="right" valign="top"><a id="a9b817690be79a6d801bb73e90c33eb70"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>sensor_state</b> = _swig_property(_moduleconnectorwrapper.RespirationData_sensor_state_get, _moduleconnectorwrapper.RespirationData_sensor_state_set)</td></tr>
<tr class="separator:a9b817690be79a6d801bb73e90c33eb70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a481ccf934c4ce556833562e71c6fe77b"><td class="memItemLeft" align="right" valign="top"><a id="a481ccf934c4ce556833562e71c6fe77b"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>respiration_rate</b> = _swig_property(_moduleconnectorwrapper.RespirationData_respiration_rate_get, _moduleconnectorwrapper.RespirationData_respiration_rate_set)</td></tr>
<tr class="separator:a481ccf934c4ce556833562e71c6fe77b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc486c58b301e7c07f0b0593db02d9f7"><td class="memItemLeft" align="right" valign="top"><a id="abc486c58b301e7c07f0b0593db02d9f7"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>distance</b> = _swig_property(_moduleconnectorwrapper.RespirationData_distance_get, _moduleconnectorwrapper.RespirationData_distance_set)</td></tr>
<tr class="separator:abc486c58b301e7c07f0b0593db02d9f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f4a3b973d9a1a50f47dd3e4dfc1c374"><td class="memItemLeft" align="right" valign="top"><a id="a9f4a3b973d9a1a50f47dd3e4dfc1c374"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>movement</b> = _swig_property(_moduleconnectorwrapper.RespirationData_movement_get, _moduleconnectorwrapper.RespirationData_movement_set)</td></tr>
<tr class="separator:a9f4a3b973d9a1a50f47dd3e4dfc1c374"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf64a8227aed4308806049303fe226c5"><td class="memItemLeft" align="right" valign="top"><a id="aaf64a8227aed4308806049303fe226c5"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>signal_quality</b> = _swig_property(_moduleconnectorwrapper.RespirationData_signal_quality_get, _moduleconnectorwrapper.RespirationData_signal_quality_set)</td></tr>
<tr class="separator:aaf64a8227aed4308806049303fe226c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Represents the respiration status data coming from the module. </p>
<h2>Attributes </h2>
<ul>
<li><p class="startli"><code>frame_counter</code> : <code>uint32_t</code> A sequential counter from the radar data.</p>
<p class="startli">Incremented for each captured frame.</p>
</li>
<li><code>sensor_state</code> : <code>uint32_t</code> This represent the steady state of the sensor module.</li>
<li><p class="startli"><code>respiration_rate</code> : <code>uint32_t</code> Respiration rate (respirations per minute / RPM).</p>
<p class="startli">Valid when SensorState is Breathing.</p>
</li>
<li><code>distance</code> : <code>float</code> The distance from the sensor to the subject (which the sensor is currently locked on to).</li>
<li><code>movement</code> : <code>float</code> Breathing pattern of closest breathing target: Detected respiratory movement in mm perpendicular to the sensor.</li>
<li><p class="startli"><code>signal_quality</code> : <code>uint32_t</code> Quality measure of the signal quality, describing the signal-to-noise ratio of the current respiration lock.</p>
<p class="startli">Value from 0 to 10, 0=low -&gt; 10=high.</p>
</li>
</ul>
<p>C++ includes: Data.hpp </p>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/moduleconnectorwrapper/__init__.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
