<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.38.0 (20140413.2041)
 -->
<!-- Title: Graphical Class Hierarchy Pages: 1 -->
<svg width="303pt" height="39pt"
 viewBox="0.00 0.00 303.00 39.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 35)">
<title>Graphical Class Hierarchy</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-35 299,-35 299,4 -4,4"/>
<!-- Node6 -->
<g id="node1" class="node"><title>Node6</title>
<polygon fill="white" stroke="#bfbfbf" points="0,-6 0,-25 77,-25 77,-6 0,-6"/>
<text text-anchor="middle" x="38.5" y="-13" font-family="Helvetica,sans-Serif" font-size="10.00">PyDataPlayer</text>
</g>
<!-- Node0 -->
<g id="node2" class="node"><title>Node0</title>
<g id="a_node2"><a xlink:href="classpymoduleconnector_1_1moduleconnector_1_1_data_player.xhtml" target="_top" xlink:title="Inherits pymoduleconnector.moduleconnectorwrapper.PyDataPlayer. ">
<polygon fill="white" stroke="black" points="113,-0.5 113,-30.5 295,-30.5 295,-0.5 113,-0.5"/>
<text text-anchor="start" x="121" y="-18.5" font-family="Helvetica,sans-Serif" font-size="10.00">pymoduleconnector.moduleconnector.</text>
<text text-anchor="middle" x="204" y="-7.5" font-family="Helvetica,sans-Serif" font-size="10.00">DataPlayer</text>
</a>
</g>
</g>
<!-- Node6&#45;&gt;Node0 -->
<g id="edge1" class="edge"><title>Node6&#45;&gt;Node0</title>
<path fill="none" stroke="midnightblue" d="M87.0275,-15.5C95.1958,-15.5 103.92,-15.5 112.779,-15.5"/>
<polygon fill="midnightblue" stroke="midnightblue" points="87.0102,-12.0001 77.0102,-15.5 87.0102,-19.0001 87.0102,-12.0001"/>
</g>
</g>
</svg>
