<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: Examples</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Examples</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">Here is a list of all examples:</div><ul>
<li><a class="el" href="_x2_m200_plot_respiration_8py-example.xhtml">X2M200_plot_respiration.py</a></li>

<li><a class="el" href="_x2_m200_read_record_8py-example.xhtml">X2M200_read_record.py</a></li>

<li><a class="el" href="_x2_m200_record_8py-example.xhtml">X2M200_record.py</a></li>

<li><a class="el" href="_x4_m200_sleep_record_8py-example.xhtml">X4M200_sleep_record.py</a></li>

<li><a class="el" href="_x4_m200__x4_m300_manipulate_noisemap_8py-example.xhtml">X4M200_X4M300_manipulate_noisemap.py</a></li>

<li><a class="el" href="_x4_m200__x4_m300_printout_infromation_8py-example.xhtml">X4M200_X4M300_printout_infromation.py</a></li>

<li><a class="el" href="_x4_m200__x4_m300_printout_pulsedoppler_data_8py-example.xhtml">X4M200_X4M300_printout_pulsedoppler_data.py</a></li>

<li><a class="el" href="_x4_m300_playback_recording_8py-example.xhtml">X4M300_playback_recording.py</a></li>

<li><a class="el" href="_x4_m300_plot_movementlist_8py-example.xhtml">X4M300_plot_movementlist.py</a></li>

<li><a class="el" href="_x4_m300_printout_presence_state_8py-example.xhtml">X4M300_printout_presence_state.py</a></li>

<li><a class="el" href="_x_e_p__x4_m200__x4_m300_access_registers_8py-example.xhtml">XEP_X4M200_X4M300_access_registers.py</a></li>

<li><a class="el" href="_x_e_p__x4_m200__x4_m300_plot_record_playback_radar_raw_data_8py-example.xhtml">XEP_X4M200_X4M300_plot_record_playback_radar_raw_data.py</a></li>

</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
