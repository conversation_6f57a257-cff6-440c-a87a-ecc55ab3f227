<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index__"></a>- _ -</h3><ul>
<li>__add_map__()
: <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_block.xhtml#a3d99e45dfc4f913609ea461bf8f3381c">pymoduleconnector.extras.regmap.RegBlock</a>
</li>
<li>__add_reg__()
: <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_map.xhtml#ac5dd168801bee542c1320d0fdc32a6e5">pymoduleconnector.extras.regmap.RegMap</a>
</li>
<li>__getitem__()
: <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_block.xhtml#a6c5f629bfd7d78ad1d08960b4cd71dbf">pymoduleconnector.extras.regmap.RegBlock</a>
, <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_map.xhtml#aa1770f1b193008cf9776e372cc183fb1">pymoduleconnector.extras.regmap.RegMap</a>
</li>
<li>__init__()
: <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_block.xhtml#a9af23de852d0bd1fd8092dbb0b6733f1">pymoduleconnector.extras.regmap.RegBlock</a>
, <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_map.xhtml#a060cf1bf1fd343a46ce7a1670fb12c9d">pymoduleconnector.extras.regmap.RegMap</a>
, <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_segment.xhtml#aa7fe549a9090a255fead66d2893e4771">pymoduleconnector.extras.regmap.RegSegment</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_data_record.xhtml#a9b26c39c0f18fa863d81f50c03878847">pymoduleconnector.moduleconnectorwrapper.DataRecord</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_data_type.xhtml#a633dbcba33419fc045bfd7c435775bb9">pymoduleconnector.moduleconnectorwrapper.DataType</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone.xhtml#a7a336d929035e170207114c0d06eae95">pymoduleconnector.moduleconnectorwrapper.DetectionZone</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone_limits.xhtml#a6ffaa3e72e88f1e0bed5b40817a96266">pymoduleconnector.moduleconnectorwrapper.DetectionZoneLimits</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_files.xhtml#ab161635b51a68e9f06172f777f869e1d">pymoduleconnector.moduleconnectorwrapper.Files</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_frame_area.xhtml#af82f543a7367a6fd5bb625daf221026e">pymoduleconnector.moduleconnectorwrapper.FrameArea</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_io_pin_control.xhtml#a869a64e3ec529dc3ddea2d65bc032889">pymoduleconnector.moduleconnectorwrapper.IoPinControl</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_periodic_noisemap_store.xhtml#a47da8aef0c735524f4094d49524ab2dd">pymoduleconnector.moduleconnectorwrapper.PeriodicNoisemapStore</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_reader.xhtml#ad02007207029a9ab693d0893f96701eb">pymoduleconnector.moduleconnectorwrapper.PyDataReader</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_data_recorder.xhtml#a880fae1cd20e7e727bf71d1b4423f738">pymoduleconnector.moduleconnectorwrapper.PyDataRecorder</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x2_m200.xhtml#a5b649c597d527882b01c40a15b091a92">pymoduleconnector.moduleconnectorwrapper.PyX2M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a2e016acba0f48d4ef0d287f9be3104b3">pymoduleconnector.moduleconnectorwrapper.PyX4M200</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a91054f29a0a9a0dce20e687ec61ed96d">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a873eeee2d3c2d5485ba82447a96c62cf">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a4e9abf9515df42cbec7e10dac634b5a2">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_respiration_data.xhtml#a4e43af1e233d47eb48872e86648b3054">pymoduleconnector.moduleconnectorwrapper.RespirationData</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_data.xhtml#a8ae5d21a853b47d07b95d86702197f51">pymoduleconnector.moduleconnectorwrapper.SleepData</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_stage_data.xhtml#ad00cdb2f45ed92236cfcffc03494ac17">pymoduleconnector.moduleconnectorwrapper.SleepStageData</a>
, <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_vital_signs_data.xhtml#a29c45d0f21dbac8c1427f5ccef200da5">pymoduleconnector.moduleconnectorwrapper.VitalSignsData</a>
</li>
<li>__iter__()
: <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_block.xhtml#a192343e00b8f8856b1b394551c674428">pymoduleconnector.extras.regmap.RegBlock</a>
, <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_map.xhtml#acf6841f0d6df7357c46ea508bc30f8be">pymoduleconnector.extras.regmap.RegMap</a>
</li>
<li>__refresh_shadow__()
: <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_map.xhtml#a4902345c70dca7aa488df49b7caf3124">pymoduleconnector.extras.regmap.RegMap</a>
</li>
<li>__setitem__()
: <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_block.xhtml#a6690d29e2f853dbce1c3940ba78de543">pymoduleconnector.extras.regmap.RegBlock</a>
, <a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg_map.xhtml#a12b0981dfac0714819d0fc2b5ced6554">pymoduleconnector.extras.regmap.RegMap</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
