<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: X2M200_read_record.py</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">X2M200_read_record.py</div>  </div>
</div><!--header-->
<div class="contents">
<p>Target module: X2M200Introduction: This is as example of how to set up the DataReader to read back a previously recorded session.</p>
<p>Command to run: "python X2M200_read_record.py -f xethru_recording_meta.dat" or "python3 X2M200_read_record.py -f xethru_recording_meta.dat"</p>
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">#!/usr/bin/env python</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"># -*- coding: utf-8 -*-</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="stringliteral">&quot;&quot;&quot; \example X2M200_read_record.py</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="stringliteral">#Target module: X2M200</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="stringliteral">#Introduction: This is as example of how to set up the DataReader to read back a previously</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="stringliteral">               recorded session.</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="stringliteral">#Command to run: &quot;python X2M200_read_record.py -f xethru_recording_meta.dat&quot; or &quot;python3 X2M200_read_record.py -f xethru_recording_meta.dat&quot;</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="stringliteral"></span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="stringliteral">&quot;&quot;&quot;</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;</div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="keyword">from</span> __future__ <span class="keyword">import</span> print_function</div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="keyword">from</span> pymoduleconnector <span class="keyword">import</span> DataReader</div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="keyword">from</span> pymoduleconnector <span class="keyword">import</span> DataRecord</div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="keyword">from</span> pymoduleconnector <span class="keyword">import</span> DataType</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;</div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="keyword">from</span> optparse <span class="keyword">import</span> OptionParser</div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="keyword">from</span> datetime <span class="keyword">import</span> datetime</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="keyword">import</span> sys</div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;</div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="keyword">def </span>process(record):</div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;    <span class="keywordflow">if</span> record.is_user_header:</div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;        print(<span class="stringliteral">&quot;read custom user header of type: {}, size: {}&quot;</span>.format(record.data_type, record.data.size()))</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;        <span class="keywordflow">return</span></div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;    print(<span class="stringliteral">&quot;read data of type: {}, size: {}&quot;</span>.format(record.data_type, record.data.size()))</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;    <span class="keywordflow">if</span> record.data_type == DataType.BasebandApDataType:</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;        print(<span class="stringliteral">&quot;process baseband ap data&quot;</span>)</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;    <span class="keywordflow">elif</span> record.data_type == DataType.SleepDataType:</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;        print(<span class="stringliteral">&quot;process sleep data&quot;</span>)</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;        csv_line = record.get_data().tobytes();</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;        print(<span class="stringliteral">&quot;  |- csv{}: {}&quot;</span>.format(<span class="stringliteral">&quot;(header)&quot;</span> <span class="keywordflow">if</span> record.is_csv_header() <span class="keywordflow">else</span> <span class="stringliteral">&#39;&#39;</span>, csv_line), end=<span class="stringliteral">&#39;&#39;</span>)</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;        <span class="keywordflow">if</span> <span class="keywordflow">not</span> record.is_csv_header():</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;            sleep_data = record.to_sleep_data()</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;            print(<span class="stringliteral">&quot;  |- frame counter: {}&quot;</span>.format(sleep_data.frame_counter))</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;            print(<span class="stringliteral">&quot;  |- sensor state: {}&quot;</span>.format(sleep_data.sensor_state))</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;            print(<span class="stringliteral">&quot;  |- respiration rate: {}&quot;</span>.format(sleep_data.respiration_rate))</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;            print(<span class="stringliteral">&quot;  |- distance: {}&quot;</span>.format(sleep_data.distance))</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;            print(<span class="stringliteral">&quot;  |- signal quality: {}&quot;</span>.format(sleep_data.signal_quality))</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;            print(<span class="stringliteral">&quot;  |- movement slow: {}&quot;</span>.format(sleep_data.movement_slow))</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;            print(<span class="stringliteral">&quot;  |- movement fast: {}&quot;</span>.format(sleep_data.movement_fast))</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;    <span class="keywordflow">elif</span> record.data_type == DataType.RespirationDataType:</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;        print(<span class="stringliteral">&quot;process respiration data&quot;</span>)</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="keyword">def </span>read_recording(meta_filename, depth):</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;    print(<span class="stringliteral">&quot;read recording: {}, depth: {}&quot;</span>.format(meta_filename, depth))</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;    reader = DataReader()</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;    <span class="keywordflow">if</span> reader.open(meta_filename, depth) != 0:</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;        print(<span class="stringliteral">&quot;ERROR: failed to open meta file: {}&quot;</span>.format(meta_filename))</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        sys.exit(1)</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;    <span class="keywordflow">while</span> <span class="keywordflow">not</span> reader.at_end():</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        record = reader.read_record()</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        <span class="keywordflow">if</span> <span class="keywordflow">not</span> record.is_valid:</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;            print(<span class="stringliteral">&quot;ERROR: failed to read data record&quot;</span>)</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;            sys.exit(1)</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        process(record)</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;    print(<span class="stringliteral">&quot;-----------------------------------------------------&quot;</span>)</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;    start_epoch = reader.get_start_epoch() / 1000.0 <span class="comment"># convert from ms to seconds</span></div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;    print(<span class="stringliteral">&quot;start time: {}&quot;</span>.format(datetime.fromtimestamp(start_epoch).strftime(<span class="stringliteral">&#39;%Y-%m-%d %H:%M:%S.%f&#39;</span>)))</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;    print(<span class="stringliteral">&quot;duration:   {} ms&quot;</span>.format(reader.get_duration()))</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;    print(<span class="stringliteral">&quot;size:       {} bytes&quot;</span>.format(reader.get_size()))</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;    print(<span class="stringliteral">&quot;session id: {}&quot;</span>.format(reader.get_session_id()))</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;    print(<span class="stringliteral">&quot;-----------------------------------------------------&quot;</span>)</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;<span class="keyword">def </span>main():</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;    parser = OptionParser()</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;    parser.add_option(<span class="stringliteral">&quot;-f&quot;</span>, <span class="stringliteral">&quot;--file&quot;</span>, dest=<span class="stringliteral">&quot;meta_filename&quot;</span>, metavar=<span class="stringliteral">&quot;FILE&quot;</span>,\</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;                      help=<span class="stringliteral">&quot;meta file from recording&quot;</span>)</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;    parser.add_option(<span class="stringliteral">&quot;--depth&quot;</span>, dest=<span class="stringliteral">&quot;depth&quot;</span>, metavar=<span class="stringliteral">&quot;NUMBER&quot;</span>, type=<span class="stringliteral">&quot;int&quot;</span>, default=-1,\</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;                      help=<span class="stringliteral">&quot;number of meta files to read in chained mode&quot;</span>)</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;    (options, args) = parser.parse_args()</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;    <span class="keywordflow">if</span> <span class="keywordflow">not</span> options.meta_filename:</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        print(<span class="stringliteral">&quot;Please specify a meta file to read (use -f &lt;file&gt; or --file &lt;file&gt;)&quot;</span>)</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;        sys.exit(1)</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    read_recording(options.meta_filename, options.depth)</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;<span class="keywordflow">if</span> __name__ == <span class="stringliteral">&quot;__main__&quot;</span>:</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    main()</div></div><!-- fragment --> </div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
