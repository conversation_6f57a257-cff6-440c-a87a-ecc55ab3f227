<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnector.ModuleConnector Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><a class="el" href="namespacepymoduleconnector_1_1moduleconnector.xhtml">moduleconnector</a></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml">ModuleConnector</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnector.ModuleConnector Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Inherits <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml" title="This class is responsible for establishing contact with the XeThru module. ">pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector</a>.  
 <a href="classpymoduleconnector_1_1moduleconnector_1_1_module_connector.xhtml#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a5ec85e8d9549294a1ab4ea0034019a54"><td class="memItemLeft" align="right" valign="top"><a id="a5ec85e8d9549294a1ab4ea0034019a54"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>__init__</b> (self, device_name=None, log_level=0)</td></tr>
<tr class="separator:a5ec85e8d9549294a1ab4ea0034019a54"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4b5886d33eab4a25b74a55af3017c6a3"><td class="memItemLeft" align="right" valign="top"><a id="a4b5886d33eab4a25b74a55af3017c6a3"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>get_x4m200</b> (self)</td></tr>
<tr class="separator:a4b5886d33eab4a25b74a55af3017c6a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a733b78988aee2ad61d1997b21ca77533"><td class="memItemLeft" align="right" valign="top"><a id="a733b78988aee2ad61d1997b21ca77533"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>get_x4m300</b> (self)</td></tr>
<tr class="separator:a733b78988aee2ad61d1997b21ca77533"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a298dedd204854b7349f147e1f7a4a829"><td class="memItemLeft" align="right" valign="top"><a id="a298dedd204854b7349f147e1f7a4a829"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>get_xep</b> (self)</td></tr>
<tr class="separator:a298dedd204854b7349f147e1f7a4a829"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af9ec8ba0c0071d5019811f9d27bbf085"><td class="memItemLeft" align="right" valign="top"><a id="af9ec8ba0c0071d5019811f9d27bbf085"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>get_x2m200</b> (self)</td></tr>
<tr class="separator:af9ec8ba0c0071d5019811f9d27bbf085"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Inherits <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml" title="This class is responsible for establishing contact with the XeThru module. ">pymoduleconnector.moduleconnectorwrapper.PythonModuleConnector</a>. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="namespacepymoduleconnector_1_1moduleconnector.xhtml#ad56b7daa5d0bcd52f411b5bbb7e77e68" title="Initiate a context managed ModuleConnector object. ">create_mc</a></dd></dl>
<p><b>Examples</b> </p><div class="fragment"><div class="line">    &gt;&gt;&gt; <span class="keyword">from</span> pymoduleconnector <span class="keyword">import</span> ModuleConnector</div><div class="line">    &gt;&gt;&gt; mc = ModuleConnector(<span class="stringliteral">&quot;/dev/ttyACM0&quot;</span>, log_level=9)</div><div class="line">    &gt;&gt;&gt; x2m200 = mc.get_x2m200()</div><div class="line">    &gt;&gt;&gt; print(hex(x2m200.ping()))</div><div class="line">    0xaaeeaeeaL</div><div class="line"></div><div class="line">Open an IP:</div><div class="line">    &gt;&gt;&gt; mc = ModuleConnector(<span class="stringliteral">&quot;tcp://127.0.0.1:3000&quot;</span>)</div></div><!-- fragment --> </div><hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/moduleconnector.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
